#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إنشاء قاعدة البيانات وإدخال بيانات تجريبية
"""

import pymysql
from datetime import datetime, date, timedelta
import random

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',  # كلمة مرور فارغة للتثبيت الافتراضي
    'charset': 'utf8mb4'
}

def create_database():
    """إنشاء قاعدة البيانات"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        cursor.execute("CREATE DATABASE IF NOT EXISTS pharmacy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # استخدام قاعدة البيانات
        cursor.execute("USE pharmacy_db")
        
        # إنشاء جدول الموردين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول العملاء
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول الأدوية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS medicines (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                category VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                quantity INT NOT NULL DEFAULT 0,
                expiry_date DATE NOT NULL,
                supplier_id INT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_id INT,
                total_amount DECIMAL(10,2) NOT NULL,
                discount DECIMAL(10,2) DEFAULT 0,
                final_amount DECIMAL(10,2) NOT NULL,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول عناصر المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sale_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sale_id INT NOT NULL,
                medicine_id INT NOT NULL,
                quantity INT NOT NULL,
                unit_price DECIMAL(10,2) NOT NULL,
                total_price DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
                FOREIGN KEY (medicine_id) REFERENCES medicines(id) ON DELETE CASCADE
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
        """)
        
        print("✅ تم إنشاء جميع الجداول بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def insert_sample_data():
    """إدخال بيانات تجريبية"""
    try:
        connection = pymysql.connect(**DB_CONFIG, database='pharmacy_db')
        cursor = connection.cursor()
        
        # إدخال موردين تجريبيين
        suppliers_data = [
            ('مذخر أدوية المنصور', '07701234567', '<EMAIL>', 'بغداد، المنصور'),
            ('شركة أدوية الرافدين', '07801234567', '<EMAIL>', 'البصرة، العشار'),
            ('مكتب أدوية النهرين العلمي', '07901234567', '<EMAIL>', 'بغداد، الكرادة'),
            ('مذخر أدوية بابل', '07712345678', '<EMAIL>', 'الحلة، مركز المدينة'),
            ('شركة أدوية آشور', '07812345678', '<EMAIL>', 'الموصل، المجموعة الثقافية')
        ]
        
        cursor.executemany("""
            INSERT INTO suppliers (name, phone, email, address) 
            VALUES (%s, %s, %s, %s)
        """, suppliers_data)
        
        # إدخال عملاء تجريبيين
        customers_data = [
            ('علي حسن كريم', '07711122233', '<EMAIL>', 'بغداد، زيونة'),
            ('زهراء أحمد جاسم', '07822233344', '<EMAIL>', 'البصرة، الجبيلة'),
            ('محمد قاسم عبد الله', '07933344455', '<EMAIL>', 'أربيل، عينكاوة'),
            ('نور صباح مهدي', '07744455566', '<EMAIL>', 'بغداد، الأعظمية'),
            ('حسن عامر خليل', '07855566677', '<EMAIL>', 'النجف، حي الأمير'),
            ('آية سعدون وليد', '07723456789', '<EMAIL>', 'كربلاء، حي الحسين'),
            ('عمر خالد إبراهيم', '07834567890', '<EMAIL>', 'كركوك، حي الواسطي'),
            ('سارة يوسف سلمان', '07945678901', '<EMAIL>', 'السليمانية، سرجنار')
        ]
        
        cursor.executemany("""
            INSERT INTO customers (name, phone, email, address) 
            VALUES (%s, %s, %s, %s)
        """, customers_data)
        
        # إدخال أدوية تجريبية
        medicines_data = [
            # مسكنات
            ('بندول اكسترا', 'مسكنات', 2500, 150, date.today() + timedelta(days=365), 1, 'مسكن للألم وخافض للحرارة'),
            ('بروفين 400', 'مسكنات', 3000, 200, date.today() + timedelta(days=400), 1, 'مضاد للالتهاب ومسكن للألم'),
            ('أسبرين 100', 'مسكنات', 1500, 300, date.today() + timedelta(days=500), 2, 'مسكن ومضاد للتجلط'),
            ('فولتارين جل', 'مسكنات', 5000, 80, date.today() + timedelta(days=300), 2, 'جل مسكن للألم الموضعي'),
            
            # مضادات حيوية
            ('أموكسيل 500', 'مضادات حيوية', 4000, 100, date.today() + timedelta(days=450), 1, 'مضاد حيوي واسع المجال'),
            ('أزيثرومايسين 250', 'مضادات حيوية', 6000, 75, date.today() + timedelta(days=380), 3, 'مضاد حيوي للجهاز التنفسي'),
            ('سيفالكسين 500', 'مضادات حيوية', 5500, 90, date.today() + timedelta(days=420), 1, 'مضاد حيوي للالتهابات البكتيرية'),
            
            # أدوية القلب
            ('كونكور 5', 'أدوية القلب', 7500, 60, date.today() + timedelta(days=600), 4, 'لعلاج ضغط الدم وأمراض القلب'),
            ('أسبيرين كارديو', 'أدوية القلب', 3000, 120, date.today() + timedelta(days=550), 4, 'لحماية القلب والأوعية الدموية'),
            ('ديجوكسين 0.25', 'أدوية القلب', 4500, 40, date.today() + timedelta(days=480), 3, 'لعلاج قصور القلب'),
            
            # أدوية السكري
            ('جلوكوفيج 500', 'أدوية السكري', 5000, 200, date.today() + timedelta(days=520), 4, 'لعلاج السكري من النوع الثاني'),
            ('جلوكوفيج XR 750', 'أدوية السكري', 6500, 150, date.today() + timedelta(days=490), 4, 'ممتد المفعول لعلاج السكري'),
            ('أماريل 2', 'أدوية السكري', 5500, 100, date.today() + timedelta(days=460), 3, 'لتحفيز إفراز الأنسولين'),
            
            # أدوية الضغط
            ('ديوفان 80', 'أدوية الضغط', 48.00, 70, date.today() + timedelta(days=620), 4, 'مثبط مستقبلات الأنجيوتنسين'),
            
            # فيتامينات
            ('فيتامين د 1000', 'فيتامينات', 18.00, 250, date.today() + timedelta(days=700), 2, 'لتقوية العظام والمناعة'),
            ('فيتامين ب المركب', 'فيتامينات', 22.00, 180, date.today() + timedelta(days=650), 2, 'لتقوية الأعصاب والطاقة'),
            ('فيتامين سي 1000', 'فيتامينات', 16.00, 300, date.today() + timedelta(days=600), 5, 'لتقوية المناعة'),
            ('أوميجا 3', 'فيتامينات', 35.00, 120, date.today() + timedelta(days=550), 5, 'لصحة القلب والدماغ'),
            
            # أدوية الجهاز الهضمي
            ('نكسيوم 40', 'أدوية الجهاز الهضمي', 55.00, 60, date.today() + timedelta(days=480), 4, 'لعلاج قرحة المعدة والحموضة'),
            ('موتيليوم 10', 'أدوية الجهاز الهضمي', 18.00, 100, date.today() + timedelta(days=420), 3, 'لعلاج الغثيان والقيء'),
            ('لوبراميد', 'أدوية الجهاز الهضمي', 12.00, 80, date.today() + timedelta(days=380), 2, 'لعلاج الإسهال'),
            
            # أدوية الجهاز التنفسي
            ('فنتولين بخاخ', 'أدوية الجهاز التنفسي', 28.00, 50, date.today() + timedelta(days=400), 3, 'موسع للشعب الهوائية'),
            ('بريدنيزولون 5', 'أدوية الجهاز التنفسي', 15.00, 90, date.today() + timedelta(days=350), 1, 'مضاد للالتهاب والحساسية'),
            ('كلاريتين 10', 'أدوية الجهاز التنفسي', 20.00, 120, date.today() + timedelta(days=500), 2, 'مضاد للحساسية'),
            
            # أدوية الأطفال
            ('بانادول شراب للأطفال', 'أدوية الأطفال', 14.00, 100, date.today() + timedelta(days=300), 1, 'خافض للحرارة ومسكن للأطفال'),
            ('فيتامين د نقط للأطفال', 'أدوية الأطفال', 25.00, 80, date.today() + timedelta(days=600), 5, 'لنمو العظام عند الأطفال'),
            ('شراب الحديد للأطفال', 'أدوية الأطفال', 18.00, 60, date.today() + timedelta(days=450), 2, 'لعلاج فقر الدم عند الأطفال'),
            
            # أدوية منتهية الصلاحية أو قريبة من الانتهاء (للاختبار)
            ('دواء منتهي الصلاحية', 'أخرى', 10.00, 5, date.today() - timedelta(days=30), 1, 'دواء منتهي الصلاحية للاختبار'),
            ('دواء قريب من الانتهاء', 'أخرى', 15.00, 8, date.today() + timedelta(days=15), 2, 'دواء قريب من انتهاء الصلاحية'),
            
            # أدوية قليلة المخزون (للاختبار)
            ('دواء قليل المخزون 1', 'أخرى', 20.00, 3, date.today() + timedelta(days=200), 3, 'دواء قليل المخزون للاختبار'),
            ('دواء قليل المخزون 2', 'أخرى', 25.00, 7, date.today() + timedelta(days=250), 4, 'دواء قليل المخزون للاختبار'),
        ]
        
        cursor.executemany("""
            INSERT INTO medicines (name, category, price, quantity, expiry_date, supplier_id, description) 
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, medicines_data)
        
        print("✅ تم إدخال البيانات التجريبية بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدخال البيانات التجريبية: {e}")
        return False

def create_sample_sales():
    """إنشاء مبيعات تجريبية"""
    try:
        connection = pymysql.connect(**DB_CONFIG, database='pharmacy_db')
        cursor = connection.cursor()
        
        # الحصول على قائمة العملاء والأدوية
        cursor.execute("SELECT id FROM customers")
        customer_ids = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id, price FROM medicines WHERE quantity > 10")
        medicines = cursor.fetchall()
        
        # إنشاء مبيعات تجريبية للأيام الماضية
        for days_ago in range(30, 0, -1):
            sale_date = datetime.now() - timedelta(days=days_ago)
            
            # إنشاء 1-3 مبيعات في اليوم
            for _ in range(random.randint(1, 3)):
                customer_id = random.choice(customer_ids) if random.random() > 0.3 else None
                discount = random.uniform(0, 20) if random.random() > 0.7 else 0
                
                # إنشاء الفاتورة
                cursor.execute("""
                    INSERT INTO sales (customer_id, total_amount, discount, final_amount, sale_date) 
                    VALUES (%s, %s, %s, %s, %s)
                """, (customer_id, 0, discount, 0, sale_date))
                
                sale_id = cursor.lastrowid
                total_amount = 0
                
                # إضافة 1-5 أصناف للفاتورة
                num_items = random.randint(1, 5)
                selected_medicines = random.sample(medicines, min(num_items, len(medicines)))
                
                for medicine_id, price in selected_medicines:
                    quantity = random.randint(1, 3)
                    item_total = price * quantity
                    total_amount += item_total
                    
                    cursor.execute("""
                        INSERT INTO sale_items (sale_id, medicine_id, quantity, unit_price, total_price) 
                        VALUES (%s, %s, %s, %s, %s)
                    """, (sale_id, medicine_id, quantity, price, item_total))
                    
                    # تحديث المخزون
                    cursor.execute("""
                        UPDATE medicines SET quantity = quantity - %s WHERE id = %s
                    """, (quantity, medicine_id))
                
                # تحديث إجمالي الفاتورة
                final_amount = total_amount - discount
                cursor.execute("""
                    UPDATE sales SET total_amount = %s, final_amount = %s WHERE id = %s
                """, (total_amount, final_amount, sale_id))
        
        print("✅ تم إنشاء المبيعات التجريبية بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المبيعات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد قاعدة البيانات...")
    
    if create_database():
        print("📊 إدخال البيانات التجريبية...")
        if insert_sample_data():
            print("💰 إنشاء مبيعات تجريبية...")
            if create_sample_sales():
                print("\n✅ تم إعداد قاعدة البيانات بنجاح!")
                print("\n📋 ملخص البيانات المُدخلة:")
                print("   • 5 موردين")
                print("   • 8 عملاء")
                print("   • 30+ دواء في فئات مختلفة")
                print("   • مبيعات تجريبية لآخر 30 يوم")
                print("\n🔧 يمكنك الآن تشغيل التطبيق باستخدام: python app.py")
            else:
                print("❌ فشل في إنشاء المبيعات التجريبية")
        else:
            print("❌ فشل في إدخال البيانات التجريبية")
    else:
        print("❌ فشل في إنشاء قاعدة البيانات")

if __name__ == "__main__":
    main()