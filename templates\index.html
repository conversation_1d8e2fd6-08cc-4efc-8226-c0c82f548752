{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="text-center mb-4">
            <i class="fas fa-pills text-primary me-3"></i>
            مرحباً بك في نظام إدارة الصيدلية
        </h1>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ total_medicines }}</div>
            <div class="stats-label">
                <i class="fas fa-pills me-2"></i>إجمالي الأدوية
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ total_customers }}</div>
            <div class="stats-label">
                <i class="fas fa-users me-2"></i>إجمالي العملاء
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ total_suppliers }}</div>
            <div class="stats-label">
                <i class="fas fa-truck me-2"></i>إجمالي الموردين
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-number">{{ "%.0f"|format(today_revenue) }}</div>
            <div class="stats-label">
                <i class="fas fa-money-bill me-2"></i>مبيعات اليوم (دينار)
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_medicine') }}" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-plus-circle me-2"></i>
                            <br>إضافة دواء جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('new_sale') }}" class="btn btn-success w-100 py-3">
                            <i class="fas fa-shopping-cart me-2"></i>
                            <br>فاتورة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-warning w-100 py-3">
                            <i class="fas fa-user-plus me-2"></i>
                            <br>إضافة عميل
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports') }}" class="btn btn-info w-100 py-3">
                            <i class="fas fa-chart-bar me-2"></i>
                            <br>عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الأدوية منتهية الصلاحية -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    أدوية منتهية الصلاحية أو قريبة من الانتهاء
                </h5>
            </div>
            <div class="card-body">
                {% if expiring_medicines %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الدواء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الكمية</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in expiring_medicines[:5] %}
                                <tr class="{% if medicine.expiry_date < today %}table-danger{% else %}table-warning{% endif %}">
                                    <td>{{ medicine.name }}</td>
                                    <td>{{ medicine.expiry_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ medicine.quantity }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if expiring_medicines|length > 5 %}
                        <p class="text-muted text-center">
                            وهناك {{ expiring_medicines|length - 5 }} أدوية أخرى...
                        </p>
                    {% endif %}
                {% else %}
                    <p class="text-center text-muted">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        لا توجد أدوية منتهية الصلاحية
                    </p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الأدوية قليلة المخزون -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    أدوية قليلة المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_medicines %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الدواء</th>
                                    <th>الكمية المتبقية</th>
                                    <th>السعر</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in low_stock_medicines[:5] %}
                                <tr class="table-danger">
                                    <td>{{ medicine.name }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ medicine.quantity }}</span>
                                    </td>
                                    <td>{{ "%.0f"|format(medicine.price) }} دينار</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if low_stock_medicines|length > 5 %}
                        <p class="text-muted text-center">
                            وهناك {{ low_stock_medicines|length - 5 }} أدوية أخرى...
                        </p>
                    {% endif %}
                {% else %}
                    <p class="text-center text-muted">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        جميع الأدوية متوفرة بكميات كافية
                    </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- مبيعات اليوم -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    ملخص مبيعات اليوم
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h3 class="text-primary">{{ today_sales_count }}</h3>
                        <p class="text-muted">عدد الفواتير</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-success">{{ "%.0f"|format(today_revenue) }} دينار</h3>
                        <p class="text-muted">إجمالي المبيعات</p>
                    </div>
                    <div class="col-md-4">
                        <h3 class="text-info">{{ "%.0f"|format(today_revenue / today_sales_count if today_sales_count > 0 else 0) }} دينار</h3>
                        <p class="text-muted">متوسط الفاتورة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}