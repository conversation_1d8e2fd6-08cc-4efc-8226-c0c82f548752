{% extends "base.html" %}

{% block title %}Add Medicine - Pharmacy Management System{% endblock %}

{% block page_title %}Add New Medicine{% endblock %}

{% block page_actions %}
<a href="{{ url_for('medicines') }}" class="btn btn-secondary">
    <i class="fas fa-arrow-left"></i> Back to Medicines
</a>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Medicine Information</h6>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Basic Information</h6>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Medicine Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="generic_name" class="form-label">Generic Name</label>
                                <input type="text" class="form-control" id="generic_name" name="generic_name">
                            </div>
                            
                            <div class="mb-3">
                                <label for="manufacturer" class="form-label">Manufacturer</label>
                                <input type="text" class="form-control" id="manufacturer" name="manufacturer">
                            </div>
                            
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select Category</option>
                                    <option value="Antibiotics">Antibiotics</option>
                                    <option value="Pain Relief">Pain Relief</option>
                                    <option value="Vitamins">Vitamins & Supplements</option>
                                    <option value="Cardiovascular">Cardiovascular</option>
                                    <option value="Respiratory">Respiratory</option>
                                    <option value="Digestive">Digestive Health</option>
                                    <option value="Diabetes">Diabetes Care</option>
                                    <option value="Skin Care">Skin Care</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dosage_form" class="form-label">Dosage Form</label>
                                <select class="form-select" id="dosage_form" name="dosage_form">
                                    <option value="">Select Form</option>
                                    <option value="Tablet">Tablet</option>
                                    <option value="Capsule">Capsule</option>
                                    <option value="Syrup">Syrup</option>
                                    <option value="Injection">Injection</option>
                                    <option value="Cream">Cream</option>
                                    <option value="Ointment">Ointment</option>
                                    <option value="Drops">Drops</option>
                                    <option value="Inhaler">Inhaler</option>
                                    <option value="Suppository">Suppository</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="strength" class="form-label">Strength</label>
                                <input type="text" class="form-control" id="strength" name="strength" placeholder="e.g., 500mg, 10ml">
                            </div>
                        </div>
                        
                        <!-- Pricing and Stock -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Pricing & Stock</h6>
                            
                            <div class="mb-3">
                                <label for="unit_price" class="form-label">Unit Price (IQD) *</label>
                                <div class="input-group">
                                    <span class="input-group-text">IQD</span>
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" step="0.01" min="0" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label">Initial Stock Quantity *</label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="reorder_level" class="form-label">Reorder Level *</label>
                                <input type="number" class="form-control" id="reorder_level" name="reorder_level" min="1" value="10" required>
                                <div class="form-text">Alert when stock reaches this level</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="batch_number" class="form-label">Batch Number</label>
                                <input type="text" class="form-control" id="batch_number" name="batch_number">
                            </div>
                            
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Medicine
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date for expiry date to today
    const expiryDateInput = document.getElementById('expiry_date');
    const today = new Date().toISOString().split('T')[0];
    expiryDateInput.min = today;
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const unitPrice = parseFloat(document.getElementById('unit_price').value);
        const stockQuantity = parseInt(document.getElementById('stock_quantity').value);
        const reorderLevel = parseInt(document.getElementById('reorder_level').value);
        
        if (unitPrice <= 0) {
            e.preventDefault();
            alert('Unit price must be greater than 0');
            return;
        }
        
        if (stockQuantity < 0) {
            e.preventDefault();
            alert('Stock quantity cannot be negative');
            return;
        }
        
        if (reorderLevel <= 0) {
            e.preventDefault();
            alert('Reorder level must be greater than 0');
            return;
        }
        
        // Check if expiry date is in the past
        const expiryDate = document.getElementById('expiry_date').value;
        if (expiryDate && new Date(expiryDate) < new Date()) {
            if (!confirm('The expiry date is in the past. Are you sure you want to continue?')) {
                e.preventDefault();
                return;
            }
        }
    });
});
</script>
{% endblock %}
