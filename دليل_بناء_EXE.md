# دليل تحويل نظام إدارة الصيدلية إلى ملف تنفيذي EXE

## 📋 المتطلبات الأساسية

1. **Python 3.8 أو أحدث** مثبت على النظام
2. **pip** (مدير الحزم) يعمل بشكل صحيح
3. **اتصال بالإنترنت** لتحميل المكتبات

## 🚀 خطوات البناء

### الطريقة الأولى: استخدام ملف Batch (الأسهل)

1. **افتح Command Prompt كمدير**
   - اضغط `Win + R`
   - اكتب `cmd`
   - اضغط `Ctrl + Shift + Enter`

2. **انتقل إلى مجلد المشروع**
   ```cmd
   cd "C:\Users\<USER>\OneDrive\Desktop\New folder (10)"
   ```

3. **شغل ملف البناء**
   ```cmd
   build_exe.bat
   ```

### الطريقة الثانية: استخدام PowerShell

1. **افتح PowerShell كمدير**
   - اضغط `Win + X`
   - اختر "Windows PowerShell (Admin)"

2. **السماح بتشغيل السكريبتات**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **انتقل إلى مجلد المشروع**
   ```powershell
   cd "C:\Users\<USER>\OneDrive\Desktop\New folder (10)"
   ```

4. **شغل ملف البناء**
   ```powershell
   .\build_exe.ps1
   ```

### الطريقة الثالثة: يدوياً

1. **تثبيت PyInstaller**
   ```cmd
   pip install pyinstaller
   ```

2. **بناء الملف التنفيذي**
   ```cmd
   pyinstaller --clean pharmacy_app.spec
   ```

## 📁 الملفات الناتجة

بعد البناء الناجح، ستجد:

```
dist/
├── نظام_إدارة_الصيدلية.exe    # الملف التنفيذي الرئيسي
├── templates/                   # مجلد القوالب
├── pharmacy.db                  # قاعدة البيانات (إن وجدت)
└── ملفات أخرى مطلوبة للتشغيل
```

## 🔧 استكشاف الأخطاء وحلها

### مشكلة: "PyInstaller غير موجود"
**الحل:**
```cmd
pip install --upgrade pip
pip install pyinstaller
```

### مشكلة: "خطأ في الوصول للملفات"
**الحل:**
- تأكد من تشغيل Command Prompt كمدير
- تأكد من عدم وجود برامج مكافحة الفيروسات تحجب العملية

### مشكلة: "فشل في بناء EXE"
**الحل:**
1. احذف مجلدات `build` و `dist`
2. شغل الأمر مرة أخرى
3. تأكد من وجود جميع الملفات المطلوبة

### مشكلة: "EXE لا يعمل على أجهزة أخرى"
**الحل:**
- تأكد من نسخ مجلد `dist` كاملاً
- تأكد من وجود ملف `pharmacy.db` في نفس مجلد EXE

## ✅ التحقق من نجاح البناء

1. **انتقل إلى مجلد dist**
2. **شغل الملف التنفيذي**
   ```
   نظام_إدارة_الصيدلية.exe
   ```
3. **يجب أن يفتح المتصفح تلقائياً على:**
   ```
   http://127.0.0.1:5000
   ```

## 📦 توزيع التطبيق

لتوزيع التطبيق على أجهزة أخرى:

1. **انسخ مجلد `dist` كاملاً**
2. **أعد تسمية المجلد إلى اسم مناسب** (مثل: "نظام إدارة الصيدلية")
3. **وزع المجلد على الأجهزة المطلوبة**
4. **شغل الملف التنفيذي من داخل المجلد**

## ⚠️ ملاحظات مهمة

- **حجم الملف:** سيكون الملف التنفيذي كبير الحجم (50-100 ميجابايت) لأنه يحتوي على Python وجميع المكتبات
- **الأمان:** قد تحتاج لإضافة استثناء في برامج مكافحة الفيروسات
- **الأداء:** قد يكون التشغيل أبطأ قليلاً من النسخة العادية
- **التحديثات:** لأي تحديث، ستحتاج لإعادة بناء EXE

## 🎯 نصائح للحصول على أفضل النتائج

1. **تأكد من عمل التطبيق بشكل صحيح قبل البناء**
2. **أغلق جميع البرامج غير الضرورية أثناء البناء**
3. **استخدم مجلد بسيط في المسار (تجنب المسارات الطويلة)**
4. **احتفظ بنسخة احتياطية من الملفات الأصلية**

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من اتباع الخطوات بالترتيب
2. تحقق من رسائل الخطأ في Command Prompt
3. جرب الطرق المختلفة للبناء
