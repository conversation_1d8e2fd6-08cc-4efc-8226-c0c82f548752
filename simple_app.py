#!/usr/bin/env python3
"""
Simple Pharmacy Management System
This version tries to work with minimal dependencies
"""

import os
import sys
import sqlite3
from datetime import datetime
import json

# Try to import Flask, if not available, provide instructions
try:
    from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask is not available. Please install it with:")
    print("pip install flask")
    sys.exit(1)

# Simple HTML templates as strings
LOGIN_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Pharmacy Management - Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { background-color: #007bff; color: white; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px; }
        .btn:hover { background-color: #0056b3; }
        .alert { padding: 10px; margin-bottom: 15px; border-radius: 4px; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        h2 { text-align: center; color: #333; margin-bottom: 30px; }
        .logo { text-align: center; margin-bottom: 20px; font-size: 48px; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💊</div>
        <h2>Pharmacy Management System</h2>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">Login</button>
        </form>
        
        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
            Default login: <strong>admin</strong> / <strong>admin123</strong>
        </div>
    </div>
</body>
</html>
"""

DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Pharmacy Management - Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background-color: #f5f5f5; }
        .header { background-color: #007bff; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        .btn { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background-color: #0056b3; }
        .btn-success { background-color: #28a745; }
        .btn-success:hover { background-color: #218838; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .actions { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .action-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .action-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.15); transition: all 0.3s; }
        .currency { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>💊 Pharmacy Management System</h1>
        <div>
            Welcome, {{ session.username }} | 
            <a href="/logout" style="color: white; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ total_medicines }}</div>
                <div class="stat-label">Total Medicines</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ low_stock_medicines }}</div>
                <div class="stat-label">Low Stock Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ today_sales }}</div>
                <div class="stat-label">Today's Sales</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">Active</div>
                <div class="stat-label">System Status</div>
            </div>
        </div>
        
        <div class="card">
            <h3>Quick Actions</h3>
            <div class="actions">
                <div class="action-card">
                    <h4>💰 New Sale</h4>
                    <p>Process a new sale transaction</p>
                    <a href="/new_sale" class="btn btn-success">Start Sale</a>
                </div>
                <div class="action-card">
                    <h4>💊 Add Medicine</h4>
                    <p>Add new medicine to inventory</p>
                    <a href="/add_medicine" class="btn">Add Medicine</a>
                </div>
                <div class="action-card">
                    <h4>📋 View Medicines</h4>
                    <p>Browse medicine inventory</p>
                    <a href="/medicines" class="btn">View Inventory</a>
                </div>
                <div class="action-card">
                    <h4>📊 Sales History</h4>
                    <p>View sales transactions</p>
                    <a href="/sales" class="btn">View Sales</a>
                </div>
            </div>
        </div>
        
        {% if recent_sales %}
        <div class="card">
            <h3>Recent Sales</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Sale ID</th>
                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Customer</th>
                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Amount</th>
                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Date</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in recent_sales %}
                    <tr>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">#{{ sale[0] }}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">{{ sale[1] or 'Walk-in Customer' }}</td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;"><span class="currency">{{ "%.0f"|format(sale[2]) }} IQD</span></td>
                        <td style="padding: 10px; border-bottom: 1px solid #eee;">{{ sale[3] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
</body>
</html>
"""

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Database setup
DATABASE = 'pharmacy.db'

def init_db():
    """Initialize the database with required tables"""
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'pharmacist',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Medicines table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS medicines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            generic_name TEXT,
            manufacturer TEXT,
            category TEXT,
            dosage_form TEXT,
            strength TEXT,
            unit_price REAL NOT NULL,
            stock_quantity INTEGER DEFAULT 0,
            reorder_level INTEGER DEFAULT 10,
            expiry_date DATE,
            batch_number TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Sales table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_name TEXT,
            customer_phone TEXT,
            total_amount REAL NOT NULL,
            payment_method TEXT DEFAULT 'cash',
            sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Sale items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sale_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sale_id INTEGER NOT NULL,
            medicine_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            FOREIGN KEY (sale_id) REFERENCES sales (id),
            FOREIGN KEY (medicine_id) REFERENCES medicines (id)
        )
    ''')
    
    # Create default admin user if not exists
    cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('admin',))
    if cursor.fetchone()[0] == 0:
        # Simple password hashing (in production, use proper hashing)
        import hashlib
        password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', password_hash, 'admin'))
    
    conn.commit()
    conn.close()

def verify_password(stored_password, provided_password):
    """Simple password verification"""
    import hashlib
    return stored_password == hashlib.sha256(provided_password.encode()).hexdigest()

@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    
    # Get statistics
    cursor.execute('SELECT COUNT(*) FROM medicines')
    total_medicines = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM medicines WHERE stock_quantity <= reorder_level')
    low_stock_medicines = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM sales WHERE DATE(sale_date) = DATE("now")')
    today_sales = cursor.fetchone()[0]
    
    # Get recent sales
    cursor.execute('''
        SELECT id, customer_name, total_amount, sale_date 
        FROM sales 
        ORDER BY sale_date DESC 
        LIMIT 5
    ''')
    recent_sales = cursor.fetchall()
    
    conn.close()
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                total_medicines=total_medicines,
                                low_stock_medicines=low_stock_medicines,
                                today_sales=today_sales,
                                recent_sales=recent_sales)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, password_hash, role FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        
        if user and verify_password(user[2], password):
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['role'] = user[3]
            flash('Login successful!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Invalid username or password!', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/medicines')
def medicines():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return "<h1>Medicines page coming soon!</h1><a href='/'>Back to Dashboard</a>"

@app.route('/add_medicine')
def add_medicine():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return "<h1>Add Medicine page coming soon!</h1><a href='/'>Back to Dashboard</a>"

@app.route('/sales')
def sales():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return "<h1>Sales page coming soon!</h1><a href='/'>Back to Dashboard</a>"

@app.route('/new_sale')
def new_sale():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return "<h1>New Sale page coming soon!</h1><a href='/'>Back to Dashboard</a>"

if __name__ == '__main__':
    print("Initializing Pharmacy Management System...")
    init_db()
    print("Database initialized successfully!")
    print("Starting Flask application...")
    print("Open your browser and go to: http://localhost:5000")
    print("Default login: admin / admin123")
    app.run(debug=True, host='0.0.0.0', port=5000)
