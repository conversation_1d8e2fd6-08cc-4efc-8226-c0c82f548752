#!/usr/bin/env python3
"""
نسخة محسّنة من نظام إدارة الصيدلية للعمل مع PyInstaller
هذا الملف يحل مشاكل التحويل إلى exe
"""

import sys
import os
import webbrowser
import threading
import time
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, timedelta, timezone
import json
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash

# إصلاح مسار الملفات للـ exe
def get_resource_path(relative_path):
    """الحصول على المسار الصحيح للملفات سواء في وضع التطوير أو exe"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# إنشاء التطبيق مع المسار الصحيح للقوالب والملفات الثابتة
template_dir = get_resource_path('templates')
static_dir = get_resource_path('static')

app = Flask(__name__, 
           template_folder=template_dir,
           static_folder=static_dir)

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'pharmacy-management-secret-key-2024'

# مسار قاعدة البيانات - في نفس مجلد الـ exe
db_path = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), 'pharmacy.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='pharmacist')
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class Supplier(db.Model):
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class Medicine(db.Model):
    __tablename__ = 'medicines'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    category = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=0)
    expiry_date = db.Column(db.Date, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # العلاقات
    supplier = db.relationship('Supplier', backref='medicines')

class Sale(db.Model):
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_name = db.Column(db.String(200))
    customer_phone = db.Column(db.String(20))
    total_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')
    sale_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # العلاقات
    user = db.relationship('User', backref='sales')

class SaleItem(db.Model):
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicines.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    
    # العلاقات
    sale = db.relationship('Sale', backref='items')
    medicine = db.relationship('Medicine', backref='sale_items')

# دوال مساعدة
def days_until_expiry(expiry_date):
    if not expiry_date:
        return None
    today = date.today()
    return (expiry_date - today).days

app.jinja_env.globals.update(days_until_expiry=days_until_expiry)

def init_database():
    """إنشاء قاعدة البيانات والبيانات الأولية"""
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin_user)
        
        # إنشاء مورد افتراضي
        if not Supplier.query.first():
            default_supplier = Supplier(
                name='مورد افتراضي',
                contact_person='مدير المبيعات',
                phone='07XX-XXX-XXXX',
                email='<EMAIL>',
                address='بغداد، العراق'
            )
            db.session.add(default_supplier)
        
        db.session.commit()

# الصفحات
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # إحصائيات لوحة التحكم
    total_medicines = Medicine.query.count()
    total_sales = Sale.query.count()
    total_suppliers = Supplier.query.count()
    
    # الأدوية منتهية الصلاحية أو قريبة من الانتهاء
    today = date.today()
    expiring_soon = Medicine.query.filter(
        Medicine.expiry_date <= today + timedelta(days=30)
    ).count()
    
    # المبيعات الأخيرة
    recent_sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
    
    return render_template('dashboard.html',
                         total_medicines=total_medicines,
                         total_sales=total_sales,
                         total_suppliers=total_suppliers,
                         expiring_soon=expiring_soon,
                         recent_sales=recent_sales)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة!', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('login'))

@app.route('/medicines')
def medicines():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Medicine.query
    
    if search:
        query = query.filter(Medicine.name.contains(search))
    
    medicines = query.paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('medicines.html', medicines=medicines, search=search)

@app.route('/add_medicine', methods=['GET', 'POST'])
def add_medicine():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        try:
            medicine = Medicine(
                name=request.form['name'],
                category=request.form['category'],
                price=float(request.form['price']),
                quantity=int(request.form['quantity']),
                expiry_date=datetime.strptime(request.form['expiry_date'], '%Y-%m-%d').date(),
                supplier_id=int(request.form['supplier_id']),
                description=request.form.get('description', '')
            )
            
            db.session.add(medicine)
            db.session.commit()
            
            flash('تم إضافة الدواء بنجاح!', 'success')
            return redirect(url_for('medicines'))
            
        except Exception as e:
            flash(f'خطأ في إضافة الدواء: {str(e)}', 'error')
    
    suppliers = Supplier.query.all()
    return render_template('add_medicine.html', suppliers=suppliers)

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🏥 نظام إدارة الصيدلية")
    print("=" * 40)
    print("📊 تهيئة قاعدة البيانات...")
    
    try:
        init_database()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        print("🌐 بدء تشغيل الخادم...")
        print("📱 سيتم فتح المتصفح تلقائياً...")
        print("🔗 الرابط: http://127.0.0.1:5000")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 40)
        
        # فتح المتصفح في خيط منفصل
        threading.Thread(target=open_browser, daemon=True).start()
        
        # تشغيل التطبيق
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
