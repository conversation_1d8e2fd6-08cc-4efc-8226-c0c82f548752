#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to check suppliers functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, Supplier
    from flask import url_for
    
    print("✅ All imports successful")
    
    # Test the suppliers functionality
    with app.app_context():
        # Test suppliers route
        with app.test_client() as client:
            print("🧪 Testing suppliers page...")
            
            # Test GET request to suppliers page
            try:
                response = client.get('/suppliers')
                print(f"Status code: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Suppliers page loads successfully")
                    print(f"Response length: {len(response.data)} bytes")
                else:
                    print(f"❌ Suppliers page failed with status: {response.status_code}")
                    print(f"Response: {response.data.decode('utf-8')}")
            except Exception as e:
                print(f"❌ Error loading suppliers page: {e}")
                import traceback
                traceback.print_exc()
            
            # Test add supplier page
            print("\n🧪 Testing add supplier page...")
            try:
                response = client.get('/suppliers/add')
                print(f"Status code: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Add supplier page loads successfully")
                else:
                    print(f"❌ Add supplier page failed with status: {response.status_code}")
                    print(f"Response: {response.data.decode('utf-8')}")
            except Exception as e:
                print(f"❌ Error loading add supplier page: {e}")
                import traceback
                traceback.print_exc()
            
            # Test adding a supplier
            print("\n🧪 Testing supplier creation...")
            try:
                response = client.post('/suppliers/add', data={
                    'name': 'Test Supplier',
                    'phone': '0512345678',
                    'email': '<EMAIL>',
                    'address': 'Test Supplier Address'
                })
                
                print(f"Status code: {response.status_code}")
                if response.status_code == 302:  # Redirect after successful creation
                    print("✅ Supplier creation successful (redirected)")
                else:
                    print(f"❌ Supplier creation failed with status: {response.status_code}")
                    print(f"Response: {response.data.decode('utf-8')}")
            except Exception as e:
                print(f"❌ Error creating supplier: {e}")
                import traceback
                traceback.print_exc()
            
            # Check if supplier was created
            try:
                supplier = Supplier.query.filter_by(name='Test Supplier').first()
                if supplier:
                    print("✅ Supplier was created in database")
                    print(f"Supplier: {supplier.name}, Phone: {supplier.phone}")
                    
                    # Test edit supplier page
                    print(f"\n🧪 Testing edit supplier page for ID {supplier.id}...")
                    try:
                        response = client.get(f'/suppliers/edit/{supplier.id}')
                        print(f"Status code: {response.status_code}")
                        
                        if response.status_code == 200:
                            print("✅ Edit supplier page loads successfully")
                        else:
                            print(f"❌ Edit supplier page failed with status: {response.status_code}")
                            print(f"Response: {response.data.decode('utf-8')}")
                    except Exception as e:
                        print(f"❌ Error loading edit supplier page: {e}")
                        import traceback
                        traceback.print_exc()
                    
                    # Clean up - delete test supplier
                    db.session.delete(supplier)
                    db.session.commit()
                    print("🧹 Test supplier cleaned up")
                else:
                    print("❌ Supplier was not created in database")
            except Exception as e:
                print(f"❌ Error checking supplier in database: {e}")
                import traceback
                traceback.print_exc()
                
        print("\n🎉 Supplier functionality test completed!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
