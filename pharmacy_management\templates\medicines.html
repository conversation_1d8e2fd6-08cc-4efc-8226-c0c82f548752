{% extends "base.html" %}

{% block title %}Medicines - Pharmacy Management System{% endblock %}

{% block page_title %}Medicines{% endblock %}

{% block page_actions %}
<a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
    <i class="fas fa-plus"></i> Add Medicine
</a>
{% endblock %}

{% block content %}
<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" placeholder="Search medicines..." value="{{ search }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        {% if search %}
        <a href="{{ url_for('medicines') }}" class="btn btn-outline-secondary">
            <i class="fas fa-times"></i> Clear Search
        </a>
        {% endif %}
    </div>
</div>

<!-- Medicines Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            Medicine Inventory
            {% if search %}
            - Search results for "{{ search }}"
            {% endif %}
        </h6>
    </div>
    <div class="card-body">
        {% if medicines.items %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Name</th>
                        <th>Generic Name</th>
                        <th>Manufacturer</th>
                        <th>Category</th>
                        <th>Strength</th>
                        <th>Price (IQD)</th>
                        <th>Stock</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for medicine in medicines.items %}
                    <tr>
                        <td>
                            <strong>{{ medicine.name }}</strong>
                            <br><small class="text-muted">{{ medicine.dosage_form }}</small>
                        </td>
                        <td>{{ medicine.generic_name or '-' }}</td>
                        <td>{{ medicine.manufacturer or '-' }}</td>
                        <td>
                            {% if medicine.category %}
                            <span class="badge bg-secondary">{{ medicine.category }}</span>
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>{{ medicine.strength or '-' }}</td>
                        <td><span class="currency">{{ medicine.unit_price }}</span></td>
                        <td>
                            <span class="badge bg-{{ 'danger' if medicine.stock_quantity <= medicine.reorder_level else 'success' }}">
                                {{ medicine.stock_quantity }}
                            </span>
                        </td>
                        <td>
                            {% if medicine.stock_quantity <= medicine.reorder_level %}
                            <span class="badge bg-warning">
                                <i class="fas fa-exclamation-triangle"></i> Low Stock
                            </span>
                            {% elif medicine.stock_quantity == 0 %}
                            <span class="badge bg-danger">
                                <i class="fas fa-times"></i> Out of Stock
                            </span>
                            {% else %}
                            <span class="badge bg-success">
                                <i class="fas fa-check"></i> In Stock
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewMedicine({{ medicine.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="showComingSoon()">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if medicines.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if medicines.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('medicines', page=medicines.prev_num, search=search) }}">Previous</a>
                </li>
                {% endif %}

                {% for page_num in medicines.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != medicines.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('medicines', page=page_num, search=search) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if medicines.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('medicines', page=medicines.next_num, search=search) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-pills fa-3x text-muted mb-3"></i>
            {% if search %}
            <h5>No medicines found</h5>
            <p class="text-muted">No medicines match your search criteria.</p>
            <a href="{{ url_for('medicines') }}" class="btn btn-outline-primary">View All Medicines</a>
            {% else %}
            <h5>No medicines in inventory</h5>
            <p class="text-muted">Start by adding your first medicine to the inventory.</p>
            <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">Add First Medicine</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Medicine Details Modal -->
<div class="modal fade" id="medicineModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Medicine Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="medicineDetails">
                <!-- Medicine details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewMedicine(medicineId) {
    // This would typically fetch medicine details via AJAX
    // For now, show a placeholder
    document.getElementById('medicineDetails').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Loading medicine details...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('medicineModal'));
    modal.show();
    
    // Simulate loading
    setTimeout(() => {
        document.getElementById('medicineDetails').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Medicine details view is coming soon!
            </div>
        `;
    }, 1000);
}
</script>
{% endblock %}
