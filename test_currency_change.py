#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify currency change to Iraqi Dinar
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, Medicine, Customer, Supplier, Sale
    
    print("🎯 اختبار تحويل العملة إلى الدينار العراقي")
    print("=" * 50)
    
    # Test all main pages
    with app.app_context():
        with app.test_client() as client:
            
            pages_to_test = [
                ('/', 'الصفحة الرئيسية'),
                ('/medicines', 'صفحة الأدوية'),
                ('/customers', 'صفحة العملاء'),
                ('/suppliers', 'صفحة الموردين'),
                ('/sales', 'صفحة المبيعات'),
                ('/sales/new', 'فاتورة جديدة'),
                ('/reports', 'صفحة التقارير'),
                ('/reports/sales', 'تقرير المبيعات'),
                ('/reports/inventory', 'تقرير المخزون')
            ]
            
            all_passed = True
            currency_issues = []
            
            for url, name in pages_to_test:
                try:
                    response = client.get(url)
                    if response.status_code == 200:
                        content = response.data.decode('utf-8')
                        
                        # Check for old currency (ريال)
                        riyal_count = content.count('ريال')
                        dinar_count = content.count('دينار')
                        
                        if riyal_count > 0:
                            currency_issues.append(f"{name}: وجد {riyal_count} مرة 'ريال'")
                            all_passed = False
                        
                        print(f"✅ {name}: يعمل بشكل صحيح - دينار: {dinar_count}, ريال: {riyal_count}")
                    else:
                        print(f"❌ {name}: خطأ {response.status_code}")
                        all_passed = False
                except Exception as e:
                    print(f"❌ {name}: خطأ - {e}")
                    all_passed = False
            
            print("\n" + "=" * 50)
            
            if currency_issues:
                print("⚠️  مشاكل العملة المتبقية:")
                for issue in currency_issues:
                    print(f"   - {issue}")
            
            if all_passed and not currency_issues:
                print("🎉 تم تحويل جميع العملات إلى الدينار العراقي بنجاح!")
                print("\n✨ التحديثات المطبقة:")
                print("   ✅ الصفحة الرئيسية: دينار")
                print("   ✅ صفحة الأدوية: دينار")
                print("   ✅ صفحة العملاء: دينار")
                print("   ✅ صفحة الموردين: دينار")
                print("   ✅ صفحة المبيعات: دينار")
                print("   ✅ فاتورة جديدة: دينار")
                print("   ✅ تقارير المبيعات: دينار")
                print("   ✅ تقرير المخزون: دينار")
                print("   ✅ تم تغيير التنسيق من %.2f إلى %.0f (أرقام صحيحة)")
                print("   ✅ تم تحديث JavaScript للأرقام الصحيحة")
                
                print("\n🌐 يمكنك الآن تشغيل التطبيق:")
                print("   python app.py")
                print("   أو")
                print("   python start_server.py")
                
            else:
                print("❌ هناك مشاكل تحتاج إصلاح")
                
        print("\n" + "=" * 50)
        print("🏁 انتهى اختبار العملة")
        
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
