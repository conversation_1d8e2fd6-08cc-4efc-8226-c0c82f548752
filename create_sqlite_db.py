#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إنشاء قاعدة البيانات SQLite وإدخال بيانات تجريبية
"""

import sqlite3
from datetime import datetime, date, timedelta
import random
import os

# مسار قاعدة البيانات
DB_PATH = 'pharmacy.db'

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    try:
        # حذف قاعدة البيانات إذا كانت موجودة
        if os.path.exists(DB_PATH):
            os.remove(DB_PATH)
        
        connection = sqlite3.connect(DB_PATH)
        cursor = connection.cursor()
        
        # إنشاء جدول الموردين
        cursor.execute("""
            CREATE TABLE suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول العملاء
        cursor.execute("""
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إنشاء جدول الأدوية
        cursor.execute("""
            CREATE TABLE medicines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT NOT NULL,
                price NUMERIC(10, 2) NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                expiry_date DATE NOT NULL,
                supplier_id INTEGER NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
            )
        """)
        
        # إنشاء جدول المبيعات
        cursor.execute("""
            CREATE TABLE sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                total_amount NUMERIC(10, 2) NOT NULL,
                discount NUMERIC(10, 2) DEFAULT 0,
                final_amount NUMERIC(10, 2) NOT NULL,
                sale_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
            )
        """)
        
        # إنشاء جدول عناصر المبيعات
        cursor.execute("""
            CREATE TABLE sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                medicine_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price NUMERIC(10, 2) NOT NULL,
                total_price NUMERIC(10, 2) NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
                FOREIGN KEY (medicine_id) REFERENCES medicines(id) ON DELETE CASCADE
            )
        """)
        
        print("✅ تم إنشاء جميع الجداول بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def insert_sample_data():
    """إدخال بيانات تجريبية"""
    try:
        connection = sqlite3.connect(DB_PATH)
        cursor = connection.cursor()
        
        # إدخال موردين تجريبيين
        suppliers_data = [
            ('مذخر أدوية المنصور', '07701234567', '<EMAIL>', 'بغداد، المنصور'),
            ('شركة أدوية الرافدين', '07801234567', '<EMAIL>', 'البصرة، العشار'),
            ('مكتب أدوية النهرين العلمي', '07901234567', '<EMAIL>', 'بغداد، الكرادة'),
            ('مذخر أدوية بابل', '07712345678', '<EMAIL>', 'الحلة، مركز المدينة'),
            ('شركة أدوية آشور', '07812345678', '<EMAIL>', 'الموصل، المجموعة الثقافية')
        ]
        
        cursor.executemany("""
            INSERT INTO suppliers (name, phone, email, address) 
            VALUES (?, ?, ?, ?)
        """, suppliers_data)
        
        # إدخال عملاء تجريبيين
        customers_data = [
            ('علي حسن كريم', '07711122233', '<EMAIL>', 'بغداد، زيونة'),
            ('زهراء أحمد جاسم', '07822233344', '<EMAIL>', 'البصرة، الجبيلة'),
            ('محمد قاسم عبد الله', '07933344455', '<EMAIL>', 'أربيل، عينكاوة'),
            ('نور صباح مهدي', '07744455566', '<EMAIL>', 'بغداد، الأعظمية'),
            ('حسن عامر خليل', '07855566677', '<EMAIL>', 'النجف، حي الأمير')
        ]
        
        cursor.executemany("""
            INSERT INTO customers (name, phone, email, address) 
            VALUES (?, ?, ?, ?)
        """, customers_data)
        
        # إدخال أدوية تجريبية
        medicines_data = [
            ('بندول اكسترا', 'مسكنات', 2500, 150, (date.today() + timedelta(days=365)).isoformat(), 1, 'مسكن للألم وخافض للحرارة'),
            ('بروفين 400', 'مسكنات', 3000, 200, (date.today() + timedelta(days=400)).isoformat(), 1, 'مضاد للالتهاب ومسكن للألم'),
            ('أموكسيل 500', 'مضادات حيوية', 4000, 100, (date.today() + timedelta(days=450)).isoformat(), 1, 'مضاد حيوي واسع المجال'),
            ('كونكور 5', 'أدوية القلب', 7500, 60, (date.today() + timedelta(days=600)).isoformat(), 2, 'لعلاج ضغط الدم وأمراض القلب'),
            ('جلوكوفيج 500', 'أدوية السكري', 5000, 200, (date.today() + timedelta(days=520)).isoformat(), 2, 'لعلاج السكري من النوع الثاني'),
            ('فيتامين د3 5000', 'فيتامينات', 10000, 250, (date.today() + timedelta(days=700)).isoformat(), 3, 'لتقوية العظام والمناعة'),
            ('أوميبرازول 20', 'أدوية الجهاز الهضمي', 6000, 60, (date.today() + timedelta(days=480)).isoformat(), 3, 'لعلاج قرحة المعدة والحموضة'),
            ('فنتولين بخاخ', 'أدوية الجهاز التنفسي', 8000, 50, (date.today() + timedelta(days=400)).isoformat(), 4, 'موسع للشعب الهوائية'),
            ('باراسيتامول شراب للأطفال', 'أدوية الأطفال', 2000, 100, (date.today() + timedelta(days=300)).isoformat(), 4, 'خافض للحرارة ومسكن للأطفال'),
            ('دواء منتهي الصلاحية', 'أخرى', 1000, 5, (date.today() - timedelta(days=30)).isoformat(), 5, 'دواء منتهي الصلاحية للاختبار'),
            ('دواء قليل المخزون', 'أخرى', 1500, 3, (date.today() + timedelta(days=200)).isoformat(), 5, 'دواء قليل المخزون للاختبار'),
        ]
        
        cursor.executemany("""
            INSERT INTO medicines (name, category, price, quantity, expiry_date, supplier_id, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, medicines_data)
        
        print("✅ تم إدخال البيانات التجريبية بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدخال البيانات التجريبية: {e}")
        return False

def create_sample_sales():
    """إنشاء مبيعات تجريبية"""
    try:
        connection = sqlite3.connect(DB_PATH)
        cursor = connection.cursor()
        
        # الحصول على قائمة العملاء والأدوية
        cursor.execute("SELECT id FROM customers")
        customer_ids = [row[0] for row in cursor.fetchall()]
        
        cursor.execute("SELECT id, price FROM medicines WHERE quantity > 10")
        medicines = cursor.fetchall()
        
        # إنشاء مبيعات تجريبية للأيام الماضية
        for days_ago in range(10, 0, -1):
            sale_date = (datetime.now() - timedelta(days=days_ago)).isoformat()
            
            # إنشاء 1-2 مبيعات في اليوم
            for _ in range(random.randint(1, 2)):
                customer_id = random.choice(customer_ids) if random.random() > 0.3 else None
                discount = random.randint(0, 1000) if random.random() > 0.8 else 0
                
                # إنشاء الفاتورة
                cursor.execute("""
                    INSERT INTO sales (customer_id, total_amount, discount, final_amount, sale_date) 
                    VALUES (?, ?, ?, ?, ?)
                """, (customer_id, 0, discount, 0, sale_date))
                
                sale_id = cursor.lastrowid
                total_amount = 0
                
                # إضافة 1-3 أصناف للفاتورة
                num_items = random.randint(1, 3)
                selected_medicines = random.sample(medicines, min(num_items, len(medicines)))
                
                for medicine_id, price in selected_medicines:
                    quantity = random.randint(1, 2)
                    item_total = price * quantity
                    total_amount += item_total
                    
                    cursor.execute("""
                        INSERT INTO sale_items (sale_id, medicine_id, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?)
                    """, (sale_id, medicine_id, quantity, price, item_total))
                    
                    # تحديث المخزون
                    cursor.execute("""
                        UPDATE medicines SET quantity = quantity - ? WHERE id = ?
                    """, (quantity, medicine_id))
                
                # تحديث إجمالي الفاتورة
                final_amount = total_amount - discount
                cursor.execute("""
                    UPDATE sales SET total_amount = ?, final_amount = ? WHERE id = ?
                """, (total_amount, final_amount, sale_id))
        
        print("✅ تم إنشاء المبيعات التجريبية بنجاح")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المبيعات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد قاعدة البيانات SQLite...")
    
    if create_database():
        print("📊 إدخال البيانات التجريبية...")
        if insert_sample_data():
            print("💰 إنشاء مبيعات تجريبية...")
            if create_sample_sales():
                print("\n✅ تم إعداد قاعدة البيانات بنجاح!")
                print("\n📋 ملخص البيانات المُدخلة:")
                print("   • 5 موردين")
                print("   • 5 عملاء")
                print("   • 11 دواء في فئات مختلفة (بأسعار الدينار العراقي)")
                print("   • مبيعات تجريبية لآخر 10 أيام")
                print("\n🔧 يمكنك الآن تشغيل التطبيق باستخدام: python app.py")
            else:
                print("❌ فشل في إنشاء المبيعات التجريبية")
        else:
            print("❌ فشل في إدخال البيانات التجريبية")
    else:
        print("❌ فشل في إنشاء قاعدة البيانات")

if __name__ == "__main__":
    main()