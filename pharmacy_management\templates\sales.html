{% extends "base.html" %}

{% block title %}Sales - Pharmacy Management System{% endblock %}

{% block page_title %}Sales History{% endblock %}

{% block page_actions %}
<a href="{{ url_for('new_sale') }}" class="btn btn-primary">
    <i class="fas fa-plus"></i> New Sale
</a>
{% endblock %}

{% block content %}
<!-- Sales Table -->
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Sales Records</h6>
    </div>
    <div class="card-body">
        {% if sales.items %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Sale ID</th>
                        <th>Customer</th>
                        <th>Phone</th>
                        <th>Total Amount</th>
                        <th>Payment Method</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sale in sales.items %}
                    <tr>
                        <td>
                            <strong>#{{ sale.id }}</strong>
                        </td>
                        <td>{{ sale.customer_name or 'Walk-in Customer' }}</td>
                        <td>{{ sale.customer_phone or '-' }}</td>
                        <td><span class="currency">{{ sale.total_amount }}</span></td>
                        <td>
                            <span class="badge bg-{{ 'success' if sale.payment_method == 'cash' else 'primary' }}">
                                {{ sale.payment_method.title() }}
                            </span>
                        </td>
                        <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewSale({{ sale.id }})">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="printReceipt({{ sale.id }})">
                                    <i class="fas fa-print"></i> Print
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if sales.pages > 1 %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if sales.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('sales', page=sales.prev_num) }}">Previous</a>
                </li>
                {% endif %}

                {% for page_num in sales.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != sales.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales', page=page_num) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if sales.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('sales', page=sales.next_num) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h5>No sales recorded</h5>
            <p class="text-muted">Start by making your first sale.</p>
            <a href="{{ url_for('new_sale') }}" class="btn btn-primary">Make First Sale</a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Sale Details Modal -->
<div class="modal fade" id="saleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sale Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="saleDetails">
                <!-- Sale details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printCurrentSale()">
                    <i class="fas fa-print"></i> Print Receipt
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentSaleId = null;

function viewSale(saleId) {
    currentSaleId = saleId;
    
    // Show loading
    document.getElementById('saleDetails').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Loading sale details...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('saleModal'));
    modal.show();
    
    // Simulate loading (in real app, this would be an AJAX call)
    setTimeout(() => {
        document.getElementById('saleDetails').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Sale details view is coming soon! This will show itemized sale information.
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6>Sale Information</h6>
                    <p><strong>Sale ID:</strong> #${saleId}</p>
                    <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
                </div>
                <div class="col-md-6">
                    <h6>Customer Information</h6>
                    <p><strong>Customer:</strong> Walk-in Customer</p>
                    <p><strong>Payment:</strong> Cash</p>
                </div>
            </div>
        `;
    }, 1000);
}

function printReceipt(saleId) {
    alert(`Printing receipt for Sale #${saleId}. This feature is coming soon!`);
}

function printCurrentSale() {
    if (currentSaleId) {
        printReceipt(currentSaleId);
    }
}
</script>
{% endblock %}
