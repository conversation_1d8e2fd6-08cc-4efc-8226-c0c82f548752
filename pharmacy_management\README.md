# Pharmacy Management System

A comprehensive web-based pharmacy management system built with Flask and SQLAlchemy. This system helps pharmacies manage their inventory, sales, and customer information efficiently.

## Features

- **User Authentication**: Secure login system with role-based access
- **Medicine Management**: Add, view, and manage medicine inventory
- **Sales Processing**: Process sales with automatic stock updates
- **Dashboard**: Overview of key metrics and recent activities
- **Stock Alerts**: Automatic low stock notifications
- **Currency Support**: Iraqi Dinar (IQD) currency formatting
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Backend**: Flask (Python web framework)
- **Database**: SQLite with SQLAlchemy ORM
- **Frontend**: Bootstrap 5, HTML5, JavaScript
- **Icons**: Font Awesome
- **Authentication**: Werkzeug password hashing

## Installation

### Prerequisites

- Python 3.7 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or download the project files**

2. **Create a virtual environment** (recommended):
   ```bash
   python -m venv venv
   ```

3. **Activate the virtual environment**:
   - On Windows:
     ```bash
     venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```bash
     source venv/bin/activate
     ```

4. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

5. **Run the application**:
   ```bash
   python app.py
   ```

6. **Access the application**:
   Open your web browser and go to `http://localhost:5000`

## Default Login Credentials

- **Username**: admin
- **Password**: admin123

## Usage

### Dashboard
- View key statistics (total medicines, low stock items, today's sales)
- Quick access to common actions
- Recent sales overview

### Medicine Management
- Add new medicines with detailed information
- View and search medicine inventory
- Track stock levels and expiry dates
- Set reorder levels for automatic alerts

### Sales Processing
- Create new sales with multiple items
- Select medicines and quantities
- Process payments (cash, card, bank transfer)
- Automatic stock updates

### Features Coming Soon
- Purchase management
- Supplier management
- Detailed reporting
- User management
- Prescription tracking

## Database Schema

The system uses the following main tables:
- **Users**: System users and authentication
- **Medicines**: Medicine inventory and details
- **Sales**: Sales transactions
- **SaleItems**: Individual items in each sale
- **Suppliers**: Supplier information (planned)
- **Purchases**: Purchase records (planned)

## Configuration

### Currency Settings
The system is configured to use Iraqi Dinar (IQD) as the default currency. This can be modified in the JavaScript formatting functions in the templates.

### Database
The application uses SQLite by default, which creates a `pharmacy.db` file in the project directory. For production use, consider switching to PostgreSQL or MySQL.

## Security Notes

- Change the default admin password after first login
- Update the `SECRET_KEY` in `app.py` for production use
- Use HTTPS in production environments
- Regularly backup your database

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure all required packages are installed
2. **Database errors**: Delete `pharmacy.db` file to reset the database
3. **Port conflicts**: Change the port in `app.py` if 5000 is already in use

### Getting Help

If you encounter any issues:
1. Check the console output for error messages
2. Ensure all dependencies are properly installed
3. Verify that the virtual environment is activated

## License

This project is open source and available under the MIT License.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
