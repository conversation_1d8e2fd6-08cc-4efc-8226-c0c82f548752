🏥 نظام إدارة الصيدلية - تحويل إلى EXE
=======================================

📋 الخطوات السريعة:

1. افتح Command Prompt كمدير
2. انتقل إلى مجلد المشروع:
   cd "C:\Users\<USER>\OneDrive\Desktop\New folder (10)"

3. شغل أحد الأوامر التالية:

   الطريقة الأولى (الأسهل):
   build_exe.bat

   الطريقة الثانية:
   pip install pyinstaller
   pyinstaller --clean pharmacy_app.spec

4. انتظر حتى انتهاء البناء (قد يستغرق 5-10 دقائق)

5. ستجد الملف التنفيذي في:
   dist\نظام_إدارة_الصيدلية.exe

🚀 لتشغيل التطبيق:
- انتقل إلى مجلد dist
- شغل ملف نظام_إدارة_الصيدلية.exe
- سيفتح المتصفح تلقائياً على http://127.0.0.1:5000

⚠️ ملاحظات:
- تأكد من وجود اتصال بالإنترنت أثناء البناء
- قد يستغرق البناء وقتاً طويلاً في المرة الأولى
- حجم الملف النهائي سيكون حوالي 50-100 ميجابايت
- لتوزيع التطبيق، انسخ مجلد dist كاملاً

📞 في حالة المشاكل:
- تأكد من تشغيل Command Prompt كمدير
- احذف مجلدات build و dist وأعد المحاولة
- تأكد من تثبيت Python بشكل صحيح
