#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نسخة محسنة من التطبيق للتحويل إلى exe
"""

import os
import sys
import tempfile
from pathlib import Path

# إضافة المسار الحالي
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمد (exe)
    application_path = os.path.dirname(sys.executable)
    template_dir = os.path.join(sys._MEIPASS, 'templates')
    static_dir = os.path.join(sys._MEIPASS, 'static') if os.path.exists(os.path.join(sys._MEIPASS, 'static')) else None
else:
    # إذا كان التطبيق يعمل من Python
    application_path = os.path.dirname(os.path.abspath(__file__))
    template_dir = os.path.join(application_path, 'templates')
    static_dir = os.path.join(application_path, 'static') if os.path.exists(os.path.join(application_path, 'static')) else None

# إضافة المسار للبحث عن الوحدات
sys.path.insert(0, application_path)

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import Numeric, func
from datetime import datetime, date, timedelta
import json
from decimal import Decimal

# إنشاء التطبيق مع مسارات صحيحة
app = Flask(__name__, 
           template_folder=template_dir,
           static_folder=static_dir)

# إعدادات قاعدة البيانات
db_path = os.path.join(application_path, 'pharmacy.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = 'pharmacy-management-system-2024'

# إعدادات العملة
app.config['CURRENCY'] = 'دينار عراقي'
app.config['CURRENCY_SYMBOL'] = 'د.ع'

db = SQLAlchemy(app)

# استيراد النماذج والمسارات من الملف الأصلي
try:
    # قراءة محتوى app.py الأصلي واستخراج النماذج والمسارات
    original_app_path = os.path.join(application_path, 'app.py')
    if os.path.exists(original_app_path):
        with open(original_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تنفيذ الكود (بحذر)
        # هذا سيستورد جميع النماذج والمسارات
        exec(content.replace('app = Flask(__name__)', '# app already created')
                   .replace('app.config.from_object(Config)', '# config already set')
                   .replace('db = SQLAlchemy(app)', '# db already created'))
    
except Exception as e:
    print(f"خطأ في تحميل التطبيق الأصلي: {e}")
    # في حالة الخطأ، استخدم إعدادات أساسية
    pass

def create_database():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

def open_browser():
    """فتح المتصفح تلقائياً"""
    import webbrowser
    import threading
    import time
    
    def delayed_open():
        time.sleep(1.5)  # انتظار حتى يبدأ الخادم
        webbrowser.open('http://127.0.0.1:5000')
    
    threading.Thread(target=delayed_open, daemon=True).start()

if __name__ == '__main__':
    try:
        print("🏥 نظام إدارة الصيدلية")
        print("=" * 40)
        print(f"📁 مسار التطبيق: {application_path}")
        print(f"📁 مسار القوالب: {template_dir}")
        print(f"📁 مسار قاعدة البيانات: {db_path}")
        
        # إنشاء قاعدة البيانات
        create_database()
        
        # فتح المتصفح
        open_browser()
        
        print("\n🌐 الخادم يعمل على:")
        print("   http://127.0.0.1:5000")
        print("   http://localhost:5000")
        print("\n🔧 اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 40)
        
        # تشغيل التطبيق
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,  # إيقاف وضع التطوير في exe
            use_reloader=False  # إيقاف إعادة التحميل التلقائي
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
    finally:
        print("🏁 تم إغلاق التطبيق")
