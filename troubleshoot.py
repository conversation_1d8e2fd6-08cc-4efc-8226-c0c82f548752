#!/usr/bin/env python3
"""
Pharmacy Management System - Troubleshooting Script
This script helps diagnose and fix common issues
"""

import sys
import os
import subprocess
import platform

def print_header(title):
    print("\n" + "="*50)
    print(f" {title}")
    print("="*50)

def check_python():
    print_header("Python Installation Check")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Platform: {platform.platform()}")
    
    # Check if Python version is compatible
    if sys.version_info < (3, 7):
        print("❌ WARNING: Python 3.7+ is recommended")
        return False
    else:
        print("✅ Python version is compatible")
        return True

def check_pip():
    print_header("Pip Installation Check")
    try:
        import pip
        print(f"✅ Pip is available")
        
        # Try to run pip command
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Pip version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Pip command failed: {result.stderr}")
            return False
    except ImportError:
        print("❌ Pip is not available")
        return False

def check_flask():
    print_header("Flask Installation Check")
    try:
        import flask
        print("✅ Flask is importable")
        
        # Try to get version
        try:
            version = flask.__version__
            print(f"✅ Flask version: {version}")
        except AttributeError:
            print("⚠️  Flask imported but version not available")
        
        # Try to create a simple app
        try:
            app = flask.Flask(__name__)
            print("✅ Flask app creation successful")
            return True
        except Exception as e:
            print(f"❌ Flask app creation failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False

def check_virtual_env():
    print_header("Virtual Environment Check")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
        print(f"Virtual env path: {sys.prefix}")
        
        # Check if .venv exists
        if os.path.exists('.venv'):
            print("✅ .venv directory exists")
        else:
            print("⚠️  .venv directory not found in current directory")
        
        return True
    else:
        print("⚠️  Not running in virtual environment")
        print("This might be okay if using system Python")
        return False

def check_database():
    print_header("Database Check")
    
    db_file = "pharmacy.db"
    if os.path.exists(db_file):
        print(f"✅ Database file exists: {db_file}")
        
        # Try to connect to database
        try:
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            
            print(f"✅ Database connection successful")
            print(f"Tables found: {[table[0] for table in tables]}")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    else:
        print(f"⚠️  Database file not found: {db_file}")
        print("This is normal for first run - database will be created automatically")
        return True

def check_files():
    print_header("Required Files Check")
    
    required_files = [
        "app.py",
        "simple_app.py", 
        "requirements.txt",
        "setup_and_run.bat"
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            all_present = False
    
    # Check templates directory
    if os.path.exists("templates"):
        template_files = os.listdir("templates")
        print(f"✅ templates/ directory ({len(template_files)} files)")
    else:
        print("⚠️  templates/ directory not found")
    
    return all_present

def suggest_fixes():
    print_header("Suggested Fixes")
    
    print("Based on the checks above, here are some suggestions:")
    print()
    
    print("1. If Flask import failed:")
    print("   - Run: pip install Flask Flask-SQLAlchemy")
    print("   - Or try: python -m pip install Flask Flask-SQLAlchemy")
    print("   - Or run: setup_and_run.bat")
    print()
    
    print("2. If virtual environment issues:")
    print("   - Delete .venv folder and recreate: python -m venv .venv")
    print("   - Or use system Python instead")
    print()
    
    print("3. If permission errors:")
    print("   - Run command prompt as Administrator")
    print("   - Or use: pip install --user Flask")
    print()
    
    print("4. To start the application:")
    print("   - Run: python app.py")
    print("   - Or run: python simple_app.py")
    print("   - Or double-click: setup_and_run.bat")
    print()
    
    print("5. If port 5000 is busy:")
    print("   - Edit app.py and change port to 5001 or another port")

def main():
    print("Pharmacy Management System - Troubleshooting")
    print("This script will check your system and help identify issues")
    
    checks = [
        ("Python", check_python),
        ("Pip", check_pip),
        ("Flask", check_flask),
        ("Virtual Environment", check_virtual_env),
        ("Database", check_database),
        ("Required Files", check_files)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"❌ Error checking {name}: {e}")
            results[name] = False
    
    # Summary
    print_header("Summary")
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name}: {status}")
    
    # Overall status
    if all(results.values()):
        print("\n🎉 All checks passed! Your system should be ready to run the application.")
        print("Try running: python app.py")
    else:
        print("\n⚠️  Some issues were found. See suggestions below.")
        suggest_fixes()

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
