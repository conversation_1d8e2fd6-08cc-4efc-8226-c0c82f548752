from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///pharmacy.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='pharmacist')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Medicine(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    generic_name = db.Column(db.String(100))
    manufacturer = db.Column(db.String(100))
    category = db.Column(db.String(50))
    dosage_form = db.Column(db.String(50))
    strength = db.Column(db.String(50))
    unit_price = db.Column(db.Float, nullable=False)
    stock_quantity = db.Column(db.Integer, default=0)
    reorder_level = db.Column(db.Integer, default=10)
    expiry_date = db.Column(db.Date)
    batch_number = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_name = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    total_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

class SaleItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sale.id'), nullable=False)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicine.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

class Supplier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Purchase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
    invoice_number = db.Column(db.String(50))
    total_amount = db.Column(db.Float, nullable=False)
    purchase_date = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

class PurchaseItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchase.id'), nullable=False)
    medicine_id = db.Column(db.Integer, db.ForeignKey('medicine.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_cost = db.Column(db.Float, nullable=False)
    total_cost = db.Column(db.Float, nullable=False)
    expiry_date = db.Column(db.Date)
    batch_number = db.Column(db.String(50))

# Routes
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    # Dashboard statistics
    total_medicines = Medicine.query.count()
    low_stock_medicines = Medicine.query.filter(Medicine.stock_quantity <= Medicine.reorder_level).count()
    today_sales = Sale.query.filter(Sale.sale_date >= datetime.now().date()).count()
    
    # Recent sales
    recent_sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
    
    return render_template('dashboard.html', 
                         total_medicines=total_medicines,
                         low_stock_medicines=low_stock_medicines,
                         today_sales=today_sales,
                         recent_sales=recent_sales)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            flash('Login successful!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Invalid username or password!', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/medicines')
def medicines():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = Medicine.query
    if search:
        query = query.filter(Medicine.name.contains(search) | 
                           Medicine.generic_name.contains(search))
    
    medicines = query.paginate(page=page, per_page=20, error_out=False)
    return render_template('medicines.html', medicines=medicines, search=search)

@app.route('/add_medicine', methods=['GET', 'POST'])
def add_medicine():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        medicine = Medicine(
            name=request.form['name'],
            generic_name=request.form['generic_name'],
            manufacturer=request.form['manufacturer'],
            category=request.form['category'],
            dosage_form=request.form['dosage_form'],
            strength=request.form['strength'],
            unit_price=float(request.form['unit_price']),
            stock_quantity=int(request.form['stock_quantity']),
            reorder_level=int(request.form['reorder_level']),
            batch_number=request.form['batch_number']
        )
        
        if request.form['expiry_date']:
            medicine.expiry_date = datetime.strptime(request.form['expiry_date'], '%Y-%m-%d').date()
        
        db.session.add(medicine)
        db.session.commit()
        flash('Medicine added successfully!', 'success')
        return redirect(url_for('medicines'))
    
    return render_template('add_medicine.html')

@app.route('/sales')
def sales():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    page = request.args.get('page', 1, type=int)
    sales = Sale.query.order_by(Sale.sale_date.desc()).paginate(page=page, per_page=20, error_out=False)
    return render_template('sales.html', sales=sales)

@app.route('/new_sale')
def new_sale():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    medicines = Medicine.query.filter(Medicine.stock_quantity > 0).all()
    return render_template('new_sale.html', medicines=medicines)

@app.route('/process_sale', methods=['POST'])
def process_sale():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    try:
        data = request.get_json()
        
        # Create sale record
        sale = Sale(
            customer_name=data.get('customer_name', ''),
            customer_phone=data.get('customer_phone', ''),
            total_amount=data['total_amount'],
            payment_method=data.get('payment_method', 'cash'),
            user_id=session['user_id']
        )
        db.session.add(sale)
        db.session.flush()  # Get the sale ID
        
        # Process sale items
        for item in data['items']:
            medicine = Medicine.query.get(item['medicine_id'])
            if medicine.stock_quantity < item['quantity']:
                return jsonify({'success': False, 'message': f'Insufficient stock for {medicine.name}'})
            
            # Create sale item
            sale_item = SaleItem(
                sale_id=sale.id,
                medicine_id=item['medicine_id'],
                quantity=item['quantity'],
                unit_price=item['unit_price'],
                total_price=item['total_price']
            )
            db.session.add(sale_item)
            
            # Update medicine stock
            medicine.stock_quantity -= item['quantity']
        
        db.session.commit()
        return jsonify({'success': True, 'sale_id': sale.id})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
