{% extends "base.html" %}

{% block title %}Dashboard - Pharmacy Management System{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Medicines
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_medicines }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-pills fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Low Stock Items
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_medicines }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Today's Sales
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_sales }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            System Status
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">Active</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('new_sale') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus-circle"></i><br>
                            New Sale
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_medicine') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-pills"></i><br>
                            Add Medicine
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-info btn-lg w-100" onclick="showComingSoon()">
                            <i class="fas fa-truck"></i><br>
                            New Purchase
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-warning btn-lg w-100" onclick="showComingSoon()">
                            <i class="fas fa-chart-bar"></i><br>
                            View Reports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sales -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent Sales</h6>
                <a href="{{ url_for('sales') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Sale ID</th>
                                <th>Customer</th>
                                <th>Total Amount</th>
                                <th>Payment Method</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>#{{ sale.id }}</td>
                                <td>{{ sale.customer_name or 'Walk-in Customer' }}</td>
                                <td><span class="currency">{{ sale.total_amount }}</span></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if sale.payment_method == 'cash' else 'primary' }}">
                                        {{ sale.payment_method.title() }}
                                    </span>
                                </td>
                                <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No sales recorded yet.</p>
                    <a href="{{ url_for('new_sale') }}" class="btn btn-primary">Make First Sale</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
</style>
{% endblock %}
