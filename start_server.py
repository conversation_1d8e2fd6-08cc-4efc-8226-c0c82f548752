#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple script to start the Flask server
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == '__main__':
    try:
        print("🚀 Starting Pharmacy Management System...")
        from app import app
        print("✅ App loaded successfully")
        print("🌐 Server will be available at:")
        print("   - http://127.0.0.1:5000")
        print("   - http://localhost:5000")
        print("\n📋 Available pages:")
        print("   - الصفحة الرئيسية: /")
        print("   - إدارة الأدوية: /medicines")
        print("   - إدارة العملاء: /customers")
        print("   - إدارة الموردين: /suppliers")
        print("   - المبيعات: /sales")
        print("   - التقارير: /reports")
        print("\n🔧 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
