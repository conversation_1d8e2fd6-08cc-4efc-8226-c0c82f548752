{% extends "base.html" %}

{% block title %}إضافة مورد جديد - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المورد *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="05xxxxxxxx">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"
                                  placeholder="العنوان الكامل للمورد أو الشركة..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ المورد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>نصائح مهمة
                </h6>
                <ul class="mb-0">
                    <li>اسم المورد مطلوب، باقي البيانات اختيارية</li>
                    <li>رقم الهاتف مفيد للتواصل مع المورد</li>
                    <li>البريد الإلكتروني يمكن استخدامه للمراسلات التجارية</li>
                    <li>العنوان مفيد لمعرفة موقع المورد</li>
                    <li>يمكن ربط الأدوية بالمورد لاحقاً</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة رقم الهاتف السعودي
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        const phone = this.value;
        if (phone && !phone.match(/^05\d{8}$/)) {
            this.setCustomValidity('رقم الهاتف يجب أن يبدأ بـ 05 ويتكون من 10 أرقام');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // التحقق من صحة البريد الإلكتروني
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('input', function() {
        const email = this.value;
        if (email && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            this.setCustomValidity('البريد الإلكتروني غير صحيح');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}