{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
            <small class="text-muted">مرحباً {{ session.username }}</small>
        </h2>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_medicines }}</h4>
                        <p class="mb-0">إجمالي الأدوية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pills fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_sales }}</h4>
                        <p class="mb-0">إجمالي المبيعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_suppliers }}</h4>
                        <p class="mb-0">الموردين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ expiring_soon }}</h4>
                        <p class="mb-0">منتهية الصلاحية قريباً</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_medicine') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>إضافة دواء جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('medicines') }}" class="btn btn-info w-100">
                            <i class="fas fa-list me-2"></i>عرض الأدوية
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-success w-100">
                            <i class="fas fa-shopping-cart me-2"></i>بيع جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-secondary w-100">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- المبيعات الأخيرة -->
{% if recent_sales %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history me-2"></i>المبيعات الأخيرة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم البيع</th>
                                <th>اسم العميل</th>
                                <th>المبلغ الإجمالي</th>
                                <th>طريقة الدفع</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in recent_sales %}
                            <tr>
                                <td>#{{ sale.id }}</td>
                                <td>{{ sale.customer_name or 'غير محدد' }}</td>
                                <td>{{ "%.2f"|format(sale.total_amount) }} دينار</td>
                                <td>
                                    {% if sale.payment_method == 'cash' %}
                                        <span class="badge bg-success">نقدي</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ sale.payment_method }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}