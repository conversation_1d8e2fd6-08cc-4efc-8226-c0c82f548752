#!/usr/bin/env python3
"""
Simple test to check if Flask is working
"""

try:
    print("Testing Flask import...")
    from flask import Flask
    print("✓ Flask imported successfully")
    
    print("Testing Flask app creation...")
    app = Flask(__name__)
    print("✓ Flask app created successfully")
    
    @app.route('/')
    def hello():
        return "Hello, Flask is working!"
    
    print("✓ Route defined successfully")
    print("Flask test completed successfully!")
    print("You can now try running: python app.py")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
except Exception as e:
    print(f"✗ Error: {e}")
