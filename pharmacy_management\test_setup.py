#!/usr/bin/env python3
"""
Test script to verify the pharmacy management system setup
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flask
        print(f"✓ Flask {flask.__version__} is available")
    except ImportError as e:
        print(f"✗ Flask import failed: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print(f"✓ Flask-SQLAlchemy is available")
    except ImportError as e:
        print(f"✗ Flask-SQLAlchemy import failed: {e}")
        return False
    
    try:
        from werkzeug.security import generate_password_hash, check_password_hash
        print("✓ Werkzeug security functions are available")
    except ImportError as e:
        print(f"✗ Werkzeug security import failed: {e}")
        return False
    
    try:
        from datetime import datetime, timedelta
        print("✓ Datetime modules are available")
    except ImportError as e:
        print(f"✗ Datetime import failed: {e}")
        return False
    
    return True

def test_file_structure():
    """Test if all required files are present"""
    print("\nTesting file structure...")
    
    required_files = [
        'app.py',
        'requirements.txt',
        'templates/base.html',
        'templates/login.html',
        'templates/dashboard.html',
        'templates/medicines.html',
        'templates/add_medicine.html',
        'templates/sales.html',
        'templates/new_sale.html'
    ]
    
    all_present = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} is missing")
            all_present = False
    
    return all_present

def test_app_creation():
    """Test if the Flask app can be created"""
    print("\nTesting Flask app creation...")
    
    try:
        # Import the app
        from app import app, db
        print("✓ Flask app imported successfully")
        
        # Test app configuration
        with app.app_context():
            print("✓ App context created successfully")
            
            # Test database creation
            db.create_all()
            print("✓ Database tables created successfully")
            
            # Test if database file was created
            if os.path.exists('pharmacy.db'):
                print("✓ Database file created")
            else:
                print("✗ Database file not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ App creation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Pharmacy Management System - Setup Test")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    tests = [
        test_imports,
        test_file_structure,
        test_app_creation
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ All tests passed! The system is ready to run.")
        print("\nTo start the application, run:")
        print("python app.py")
        print("\nThen open your browser to: http://localhost:5000")
        print("Default login: admin / admin123")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        print("\nTo install missing dependencies, run:")
        print("pip install flask flask-sqlalchemy")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
