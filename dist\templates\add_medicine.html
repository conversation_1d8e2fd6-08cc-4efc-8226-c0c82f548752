{% extends "base.html" %}

{% block title %}إضافة دواء جديد - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle me-2"></i>إضافة دواء جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الدواء *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <option value="مسكنات">مسكنات</option>
                                <option value="مضادات حيوية">مضادات حيوية</option>
                                <option value="أدوية القلب">أدوية القلب</option>
                                <option value="أدوية السكري">أدوية السكري</option>
                                <option value="أدوية الضغط">أدوية الضغط</option>
                                <option value="فيتامينات">فيتامينات</option>
                                <option value="أدوية الجهاز الهضمي">أدوية الجهاز الهضمي</option>
                                <option value="أدوية الجهاز التنفسي">أدوية الجهاز التنفسي</option>
                                <option value="أدوية الأطفال">أدوية الأطفال</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">السعر (ريال) *</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" required>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="quantity" class="form-label">الكمية *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   min="0" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية *</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="supplier_id" class="form-label">المورد *</label>
                            <select class="form-select" id="supplier_id" name="supplier_id" required>
                                <option value="">اختر المورد</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                <a href="{{ url_for('add_supplier') }}" target="_blank">إضافة مورد جديد</a>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف الدواء، الاستخدامات، التحذيرات..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('medicines') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الدواء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>نصائح مهمة
                </h6>
                <ul class="mb-0">
                    <li>تأكد من إدخال تاريخ انتهاء الصلاحية بدقة</li>
                    <li>راجع السعر والكمية قبل الحفظ</li>
                    <li>اختر الفئة المناسبة لتسهيل البحث لاحقاً</li>
                    <li>أضف وصفاً مفصلاً للدواء لتسهيل التعرف عليه</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين الحد الأدنى لتاريخ انتهاء الصلاحية (غداً)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const minDate = tomorrow.toISOString().split('T')[0];
    document.getElementById('expiry_date').setAttribute('min', minDate);
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const expiryDateValue = document.getElementById('expiry_date').value;
        if (!expiryDateValue) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ انتهاء الصلاحية');
            document.getElementById('expiry_date').focus();
            return false;
        }
        
        const expiryDate = new Date(expiryDateValue);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (expiryDate <= today) {
            e.preventDefault();
            alert('تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل');
            document.getElementById('expiry_date').focus();
            return false;
        }
        
        const price = parseFloat(document.getElementById('price').value);
        if (price <= 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من صفر');
            return false;
        }
        
        const quantity = parseInt(document.getElementById('quantity').value);
        if (quantity < 0) {
            e.preventDefault();
            alert('الكمية لا يمكن أن تكون سالبة');
            return false;
        }
    });
});
</script>
{% endblock %}