import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'pharmacy-secret-key-2024'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///pharmacy.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات التطبيق
    APP_NAME = 'نظام إدارة الصيدلية'
    ITEMS_PER_PAGE = 10
    CURRENCY_SYMBOL = 'د.ع'  # العملة (دينار عراقي)
    CURRENCY_FORMAT = '{:,.0f}' # تنسيق عرض العملة بدون أرقام عشرية