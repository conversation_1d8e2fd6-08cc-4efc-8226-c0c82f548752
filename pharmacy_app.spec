# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# جمع ملفات البيانات
datas = []

# إضافة مجلد القوالب
datas += [('templates', 'templates')]

# إضافة مجلد الملفات الثابتة إذا كان موجوداً
if os.path.exists('static'):
    datas += [('static', 'static')]

# إضافة ملف قاعدة البيانات إذا كان موجوداً
if os.path.exists('pharmacy.db'):
    datas += [('pharmacy.db', '.')]

# إضافة ملف الإعدادات
datas += [('config.py', '.')]

# جمع الوحدات المخفية
hiddenimports = [
    'flask',
    'flask_sqlalchemy',
    'sqlalchemy',
    'sqlalchemy.sql.default_comparator',
    'sqlalchemy.ext.declarative',
    'jinja2',
    'werkzeug',
    'click',
    'itsdangerous',
    'markupsafe',
    'datetime',
    'decimal',
    'json'
]

# جمع ملفات Flask
datas += collect_data_files('flask')
datas += collect_data_files('jinja2')

block_cipher = None

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_الصيدلية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكنك إضافة أيقونة هنا إذا أردت
)
