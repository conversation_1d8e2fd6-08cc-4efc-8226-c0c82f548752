{% extends "base.html" %}

{% block title %}تعديل الدواء - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>تعديل الدواء: {{ medicine.name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الدواء *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ medicine.name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <option value="مسكنات" {{ 'selected' if medicine.category == 'مسكنات' }}>مسكنات</option>
                                <option value="مضادات حيوية" {{ 'selected' if medicine.category == 'مضادات حيوية' }}>مضادات حيوية</option>
                                <option value="أدوية القلب" {{ 'selected' if medicine.category == 'أدوية القلب' }}>أدوية القلب</option>
                                <option value="أدوية السكري" {{ 'selected' if medicine.category == 'أدوية السكري' }}>أدوية السكري</option>
                                <option value="أدوية الضغط" {{ 'selected' if medicine.category == 'أدوية الضغط' }}>أدوية الضغط</option>
                                <option value="فيتامينات" {{ 'selected' if medicine.category == 'فيتامينات' }}>فيتامينات</option>
                                <option value="أدوية الجهاز الهضمي" {{ 'selected' if medicine.category == 'أدوية الجهاز الهضمي' }}>أدوية الجهاز الهضمي</option>
                                <option value="أدوية الجهاز التنفسي" {{ 'selected' if medicine.category == 'أدوية الجهاز التنفسي' }}>أدوية الجهاز التنفسي</option>
                                <option value="أدوية الأطفال" {{ 'selected' if medicine.category == 'أدوية الأطفال' }}>أدوية الأطفال</option>
                                <option value="أخرى" {{ 'selected' if medicine.category == 'أخرى' }}>أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">السعر (ريال) *</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" value="{{ medicine.price }}" required>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="quantity" class="form-label">الكمية *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   min="0" value="{{ medicine.quantity }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية *</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                   value="{{ medicine.expiry_date.strftime('%Y-%m-%d') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="supplier_id" class="form-label">المورد *</label>
                            <select class="form-select" id="supplier_id" name="supplier_id" required>
                                <option value="">اختر المورد</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}" {{ 'selected' if supplier.id == medicine.supplier_id }}>
                                    {{ supplier.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف الدواء، الاستخدامات، التحذيرات...">{{ medicine.description or '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('medicines') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات الدواء الحالية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-header">
                <h6 class="mb-0">معلومات الدواء الحالية</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>تاريخ الإضافة:</strong></td>
                        <td>{{ medicine.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td><strong>المورد الحالي:</strong></td>
                        <td>{{ medicine.supplier.name if medicine.supplier else 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>حالة المخزون:</strong></td>
                        <td>
                            {% if medicine.quantity <= 10 %}
                                <span class="badge bg-warning">قليل</span>
                            {% else %}
                                <span class="badge bg-success">متوفر</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>حالة الصلاحية:</strong></td>
                        <td>
                            {% set days_left = days_until_expiry(medicine.expiry_date) %}
                            {% if days_left is not none %}
                                {% if days_left <= 0 %}
                                    <span class="badge bg-danger">منتهي الصلاحية</span>
                                {% elif days_left <= 30 %}
                                    <span class="badge bg-warning">{{ days_left }} يوم متبقي</span>
                                {% else %}
                                    <span class="badge bg-success">صالح</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>نصائح التعديل
                </h6>
                <ul class="mb-0">
                    <li>تأكد من صحة البيانات قبل الحفظ</li>
                    <li>لا تقم بتغيير تاريخ الصلاحية إلا إذا كان خاطئاً</li>
                    <li>راجع الكمية المتبقية في المخزون</li>
                    <li>تحديث السعر قد يؤثر على الفواتير الجديدة فقط</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين الحد الأدنى للتاريخ (اليوم)
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    document.getElementById('expiry_date').setAttribute('min', todayStr);
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const expiryDateValue = document.getElementById('expiry_date').value;
        if (!expiryDateValue) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ انتهاء الصلاحية');
            document.getElementById('expiry_date').focus();
            return false;
        }
        
        const newExpiryDate = new Date(expiryDateValue);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (newExpiryDate < today) {
            e.preventDefault();
            alert('تاريخ انتهاء الصلاحية لا يمكن أن يكون في الماضي');
            document.getElementById('expiry_date').focus();
            return false;
        }
        
        const price = parseFloat(document.getElementById('price').value);
        if (price <= 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من صفر');
            document.getElementById('price').focus();
            return false;
        }
        
        const quantity = parseInt(document.getElementById('quantity').value);
        if (quantity < 0) {
            e.preventDefault();
            alert('الكمية لا يمكن أن تكون سالبة');
            document.getElementById('quantity').focus();
            return false;
        }
    });
});
</script>
{% endblock %}