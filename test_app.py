#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Starting imports...")

try:
    from flask import Flask
    print("✅ Flask imported successfully")
except Exception as e:
    print(f"❌ Flask import failed: {e}")

try:
    from flask_sqlalchemy import SQLAlchemy
    print("✅ SQLAlchemy imported successfully")
except Exception as e:
    print(f"❌ SQLAlchemy import failed: {e}")

try:
    from config import Config
    print("✅ Config imported successfully")
except Exception as e:
    print(f"❌ Config import failed: {e}")

print("All imports completed!")

# Test basic app creation
try:
    app = Flask(__name__)
    app.config.from_object(Config)
    db = SQLAlchemy(app)
    print("✅ App and DB created successfully")
except Exception as e:
    print(f"❌ App creation failed: {e}")

print("Test completed!")
