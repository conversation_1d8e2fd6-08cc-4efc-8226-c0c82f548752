@echo off
echo ========================================
echo Pharmacy Management System Setup
echo ========================================
echo.

echo Step 1: Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)
echo Python is available!
echo.

echo Step 2: Removing corrupted virtual environment...
if exist .venv (
    echo Removing existing .venv directory...
    rmdir /s /q .venv
    echo Old virtual environment removed.
) else (
    echo No existing virtual environment found.
)
echo.

echo Step 3: Creating new virtual environment...
python -m venv .venv
if %errorlevel% neq 0 (
    echo ERROR: Failed to create virtual environment
    echo Trying with system Python instead...
    goto :system_install
)
echo Virtual environment created successfully!
echo.

echo Step 4: Activating virtual environment...
call .venv\Scripts\activate.bat
echo Virtual environment activated!
echo.

echo Step 5: Upgrading pip...
python -m pip install --upgrade pip
echo.

echo Step 6: Installing Flask and dependencies...
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5
if %errorlevel% neq 0 (
    echo ERROR: Failed to install packages in virtual environment
    echo Trying with system Python instead...
    goto :system_install
)
echo Packages installed successfully!
echo.

echo Step 7: Testing Flask installation...
python test_flask.py
if %errorlevel% neq 0 (
    echo WARNING: Flask test failed, trying simplified version...
    goto :run_simple
)
echo.

echo Step 8: Starting the application...
echo.
echo ========================================
echo Application is starting...
echo Open your browser and go to: http://localhost:5000
echo Default login: admin / admin123
echo Press Ctrl+C to stop the application
echo ========================================
echo.
python app.py
goto :end

:system_install
echo.
echo ========================================
echo Using System Python Installation
echo ========================================
echo.
echo Installing Flask with system Python...
pip install Flask Flask-SQLAlchemy
if %errorlevel% neq 0 (
    echo ERROR: Failed to install Flask
    echo Please try running as Administrator
    pause
    exit /b 1
)
echo.

:run_simple
echo Starting simplified application...
echo.
echo ========================================
echo Application is starting...
echo Open your browser and go to: http://localhost:5000
echo Default login: admin / admin123
echo Press Ctrl+C to stop the application
echo ========================================
echo.
python simple_app.py

:end
echo.
echo Application has stopped.
pause
