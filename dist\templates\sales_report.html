{% extends "base.html" %}

{% block title %}تقرير المبيعات - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>تقرير المبيعات
                </h5>
                <div>
                    <button onclick="window.print()" class="btn btn-success">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </button>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- فلاتر التقرير -->
                <div class="row mb-4">
                    <div class="col-12">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" 
                                       value="{{ start_date or '' }}">
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       value="{{ end_date or '' }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>تطبيق الفلتر
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- ملخص التقرير -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-receipt fa-2x mb-2"></i>
                                <h4>{{ sales|length }}</h4>
                                <small>عدد الفواتير</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4>{{ "%.2f"|format(total_revenue) }}</h4>
                                <small>إجمالي الإيرادات (دينار)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h4>{{ "%.0f"|format(total_revenue / sales|length if sales else 0) }}</h4>
                                <small>متوسط الفاتورة (دينار)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage fa-2x mb-2"></i>
                                <h4>{{ "%.0f"|format(sales|sum(attribute='discount')) }}</h4>
                                <small>إجمالي الخصومات (دينار)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المبيعات -->
                {% if sales %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>العميل</th>
                                <th>عدد الأصناف</th>
                                <th>إجمالي المبلغ</th>
                                <th>الخصم</th>
                                <th>المبلغ النهائي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr>
                                <td><strong>#{{ sale.id }}</strong></td>
                                <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    {% if sale.customer %}
                                        {{ sale.customer.name }}
                                    {% else %}
                                        <span class="text-muted">عميل غير مسجل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ sale.sale_items|length }}</span>
                                </td>
                                <td>{{ "%.0f"|format(sale.total_amount) }} دينار</td>
                                <td>
                                    {% if sale.discount > 0 %}
                                        {{ "%.0f"|format(sale.discount) }} دينار
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="text-success">{{ "%.0f"|format(sale.final_amount) }} دينار</strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="4">الإجمالي</th>
                                <th>
                                    {% set total_amount = 0 %}
                                    {% for sale in sales %}
                                        {% set total_amount = total_amount + sale.total_amount %}
                                    {% endfor %}
                                    {{ "%.0f"|format(total_amount) }} دينار
                                </th>
                                <th>
                                    {% set total_discount = 0 %}
                                    {% for sale in sales %}
                                        {% set total_discount = total_discount + sale.discount %}
                                    {% endfor %}
                                    {{ "%.0f"|format(total_discount) }} دينار
                                </th>
                                <th>{{ "%.0f"|format(total_revenue) }} دينار</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مبيعات في الفترة المحددة</h5>
                    <p class="text-muted">جرب تغيير الفترة الزمنية أو إزالة الفلاتر</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تحليل إضافي -->
{% if sales %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">أفضل العملاء</h6>
            </div>
            <div class="card-body">
                {% set customer_sales = {} %}
                {% for sale in sales %}
                    {% if sale.customer %}
                        {% if sale.customer.name in customer_sales %}
                            {% set _ = customer_sales.update({sale.customer.name: customer_sales[sale.customer.name] + sale.final_amount}) %}
                        {% else %}
                            {% set _ = customer_sales.update({sale.customer.name: sale.final_amount}) %}
                        {% endif %}
                    {% endif %}
                {% endfor %}
                
                {% if customer_sales %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>إجمالي المشتريات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer, total in customer_sales.items()|sort(attribute=1, reverse=true)[:5] %}
                                <tr>
                                    <td>{{ customer }}</td>
                                    <td>{{ "%.0f"|format(total) }} دينار</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد مبيعات لعملاء مسجلين</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">المبيعات حسب اليوم</h6>
            </div>
            <div class="card-body">
                {% set daily_sales = {} %}
                {% for sale in sales %}
                    {% set day = sale.sale_date.strftime('%Y-%m-%d') %}
                    {% if day in daily_sales %}
                        {% set _ = daily_sales.update({day: daily_sales[day] + sale.final_amount}) %}
                    {% else %}
                        {% set _ = daily_sales.update({day: sale.final_amount}) %}
                    {% endif %}
                {% endfor %}
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>إجمالي المبيعات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day, total in daily_sales.items()|sort(reverse=true)[:7] %}
                            <tr>
                                <td>{{ day }}</td>
                                <td>{{ "%.0f"|format(total) }} دينار</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي كقيمة افتراضية إذا لم تكن محددة
    if (!document.getElementById('end_date').value) {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('end_date').value = today;
    }
    
    if (!document.getElementById('start_date').value) {
        const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
        document.getElementById('start_date').value = firstDay;
    }
});

// تخصيص الطباعة
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
    }
    
    body {
        background: white !important;
    }
}
</style>
{% endblock %}