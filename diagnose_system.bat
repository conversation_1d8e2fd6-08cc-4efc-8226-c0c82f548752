@echo off
chcp 65001 >nul
echo.
echo ========================================
echo      تشخيص مشاكل نظام الصيدلية
echo ========================================
echo.

echo 🔍 فحص Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    goto :no_python
) else (
    echo ✅ Python مثبت
)

echo.
echo 🔍 فحص pip...
pip --version
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر
) else (
    echo ✅ pip متوفر
)

echo.
echo 🔍 فحص البيئة الافتراضية...
if exist ".venv" (
    echo ✅ البيئة الافتراضية موجودة
    echo 🔍 فحص تفعيل البيئة الافتراضية...
    call .venv\Scripts\activate.bat
    python -c "import sys; print('مسار Python:', sys.executable)"
) else (
    echo ⚠️ البيئة الافتراضية غير موجودة
)

echo.
echo 🔍 فحص Flask...
python -c "import flask; print('✅ Flask متوفر - الإصدار:', flask.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask غير متوفر أو معطل
    echo.
    echo 📋 قائمة الحزم المثبتة:
    pip list | findstr -i flask
    echo.
    echo 💡 الحلول المقترحة:
    echo 1. شغل: fix_flask_installation.bat
    echo 2. أو شغل: run_without_venv.bat  
    echo 3. أو شغل: run_exe_fixed.py مباشرة
    goto :end
) else (
    echo ✅ Flask يعمل بشكل صحيح
)

echo.
echo 🔍 فحص Flask-SQLAlchemy...
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask-SQLAlchemy غير متوفر
) else (
    echo ✅ Flask-SQLAlchemy يعمل بشكل صحيح
)

echo.
echo 🔍 فحص ملفات التطبيق...
if exist "app.py" (
    echo ✅ app.py موجود
) else (
    echo ❌ app.py غير موجود
)

if exist "templates" (
    echo ✅ مجلد templates موجود
) else (
    echo ❌ مجلد templates غير موجود
)

if exist "config.py" (
    echo ✅ config.py موجود
) else (
    echo ❌ config.py غير موجود
)

echo.
echo ========================================
echo           نتيجة التشخيص
echo ========================================

python -c "
try:
    import flask, flask_sqlalchemy
    print('✅ جميع المتطلبات متوفرة - يمكن تشغيل التطبيق')
    print('🚀 شغل: python app.py')
except ImportError as e:
    print('❌ مشكلة في المتطلبات:', str(e))
    print('💡 شغل: fix_flask_installation.bat')
"

goto :end

:no_python
echo.
echo ❌ Python غير مثبت!
echo.
echo 📋 خطوات الحل:
echo 1. حمل Python من: https://python.org
echo 2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
echo 3. أعد تشغيل Command Prompt
echo 4. شغل هذا الملف مرة أخرى

:end
echo.
pause
