# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# جمع ملفات البيانات
datas = []

# إضافة مجلد القوالب
datas += [('templates', 'templates')]

# إضافة مجلد الملفات الثابتة إذا كان موجوداً
if os.path.exists('static'):
    datas += [('static', 'static')]

# جمع الوحدات المخفية المطلوبة
hiddenimports = [
    'flask',
    'flask_sqlalchemy',
    'sqlalchemy',
    'sqlalchemy.sql.default_comparator',
    'sqlalchemy.ext.declarative',
    'sqlalchemy.orm',
    'sqlalchemy.orm.query',
    'sqlalchemy.orm.session',
    'sqlalchemy.engine',
    'sqlalchemy.engine.default',
    'sqlalchemy.pool',
    'jinja2',
    'jinja2.ext',
    'werkzeug',
    'werkzeug.security',
    'click',
    'itsdangerous',
    'markupsafe',
    'datetime',
    'decimal',
    'json',
    'threading',
    'webbrowser',
    'time',
    'sqlite3'
]

# جمع ملفات Flask وJinja2
try:
    datas += collect_data_files('flask')
    datas += collect_data_files('jinja2')
    datas += collect_data_files('werkzeug')
except:
    pass

block_cipher = None

a = Analysis(
    ['run_exe_fixed.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_الصيدلية_المحسن',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # تغيير إلى False لإخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
