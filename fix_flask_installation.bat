@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   إصلاح تثبيت Flask في البيئة الافتراضية
echo ========================================
echo.

echo 🔧 الخطوة 1: حذف البيئة الافتراضية المعطلة...
if exist ".venv" (
    rmdir /s /q ".venv"
    echo ✅ تم حذف البيئة الافتراضية القديمة
) else (
    echo ℹ️  لا توجد بيئة افتراضية للحذف
)

echo.
echo 🆕 الخطوة 2: إنشاء بيئة افتراضية جديدة...
python -m venv .venv
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء البيئة الافتراضية
    echo 💡 جرب استخدام Python العام بدلاً من البيئة الافتراضية
    pause
    exit /b 1
)
echo ✅ تم إنشاء البيئة الافتراضية الجديدة

echo.
echo 🔄 الخطوة 3: تفعيل البيئة الافتراضية...
call .venv\Scripts\activate.bat
echo ✅ تم تفعيل البيئة الافتراضية

echo.
echo ⬆️ الخطوة 4: تحديث pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في تحديث pip، لكن سنتابع...
)

echo.
echo 📦 الخطوة 5: تثبيت Flask والمتطلبات...
pip install Flask==2.3.3
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Flask
    echo 💡 جرب تثبيت Flask بدون إصدار محدد...
    pip install Flask
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask نهائياً
        pause
        exit /b 1
    )
)

echo.
echo 📦 تثبيت Flask-SQLAlchemy...
pip install Flask-SQLAlchemy
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Flask-SQLAlchemy
    pause
    exit /b 1
)

echo.
echo 🧪 الخطوة 6: اختبار تثبيت Flask...
python -c "import flask; print('✅ Flask version:', flask.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask ما زال لا يعمل
    echo 💡 جرب استخدام النسخة المبسطة بدلاً من ذلك
    pause
    exit /b 1
)

echo.
echo 🧪 اختبار Flask-SQLAlchemy...
python -c "import flask_sqlalchemy; print('✅ Flask-SQLAlchemy يعمل بشكل صحيح')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Flask-SQLAlchemy لا يعمل
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم إصلاح تثبيت Flask بنجاح!
echo ========================================
echo.

echo 🚀 هل تريد تشغيل التطبيق الآن؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo.
    echo 🌐 تشغيل نظام إدارة الصيدلية...
    echo 📱 سيفتح المتصفح على: http://localhost:5000
    echo 👤 المستخدم: admin
    echo 🔑 كلمة المرور: admin123
    echo.
    python app.py
)

pause
