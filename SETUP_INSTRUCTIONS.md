# Pharmacy Management System - Setup Instructions

## Problem Diagnosis
Your virtual environment appears to have corrupted Flask installation. Here's how to fix it:

## Solution 1: Fix Virtual Environment (Recommended)

### Step 1: Delete and recreate virtual environment
```bash
# Delete the existing virtual environment
rmdir /s .venv

# Create a new virtual environment
python -m venv .venv

# Activate the virtual environment
.venv\Scripts\activate

# Upgrade pip
python -m pip install --upgrade pip

# Install required packages
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5
```

### Step 2: Run the application
```bash
# Make sure virtual environment is activated
.venv\Scripts\activate

# Run the main application
python app.py
```

## Solution 2: Use System Python (Alternative)

If the virtual environment continues to have issues:

### Step 1: Install Flask globally
```bash
# Install Flask using system Python
pip install Flask Flask-SQLAlchemy
```

### Step 2: Run the simplified application
```bash
# Run the simplified version
python simple_app.py
```

## Solution 3: Manual Setup (If pip has issues)

### Step 1: Download Flask manually
1. Go to https://pypi.org/project/Flask/#files
2. Download Flask-2.3.3-py3-none-any.whl
3. Install manually: `pip install Flask-2.3.3-py3-none-any.whl`

## Quick Test

To test if Flask is working:
```bash
python test_flask.py
```

## Application Features

Once running, the application provides:

- **User Authentication**: Login with admin/admin123
- **Dashboard**: Overview of pharmacy statistics
- **Medicine Management**: Add and manage medicine inventory
- **Sales Processing**: Process sales transactions
- **Stock Management**: Track inventory levels
- **Currency Support**: Iraqi Dinar (IQD) formatting

## Default Login Credentials
- Username: `admin`
- Password: `admin123`

## Accessing the Application

Once the application is running:
1. Open your web browser
2. Go to: `http://localhost:5000`
3. Login with the default credentials
4. Start managing your pharmacy!

## Troubleshooting

### If you get "Flask not found" error:
1. Make sure Flask is installed: `pip list | findstr Flask`
2. Try installing with: `pip install --user Flask`
3. Use the simplified app: `python simple_app.py`

### If you get "Permission denied" error:
1. Run command prompt as Administrator
2. Or use: `pip install --user Flask`

### If the application won't start:
1. Check if port 5000 is available
2. Try changing the port in the code: `app.run(port=5001)`
3. Check firewall settings

## Files Included

- `app.py` - Main application with full features
- `simple_app.py` - Simplified version with basic features
- `test_flask.py` - Test script to verify Flask installation
- `requirements.txt` - List of required packages
- `templates/` - HTML templates for the web interface

## Next Steps

After getting the application running:
1. Change the default admin password
2. Add your medicine inventory
3. Start processing sales
4. Explore the dashboard features

## Support

If you continue to have issues:
1. Check Python version: `python --version` (should be 3.7+)
2. Check pip version: `pip --version`
3. Try creating a fresh virtual environment
4. Consider using conda instead of pip if available
