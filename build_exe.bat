@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    بناء نظام إدارة الصيدلية - EXE
echo ========================================
echo.

echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 🔧 تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo 🏗️  بناء الملف التنفيذي...
pyinstaller --clean pharmacy_app.spec

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الملف التنفيذي
    pause
    exit /b 1
)

echo.
echo 📁 نسخ الملفات الإضافية...
if exist "pharmacy.db" copy "pharmacy.db" "dist\"
if exist "config.py" copy "config.py" "dist\"

echo.
echo ✅ تم بناء الملف التنفيذي بنجاح!
echo.
echo 📍 الملف التنفيذي موجود في:
echo    dist\نظام_إدارة_الصيدلية.exe
echo.
echo 🚀 لتشغيل التطبيق:
echo    1. انتقل إلى مجلد dist
echo    2. شغل ملف نظام_إدارة_الصيدلية.exe
echo.
pause
