#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify that the JSON serialization fix works
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, Medicine, Supplier
    from flask import jsonify
    import json
    
    print("✅ All imports successful")
    
    # Test the to_dict method
    with app.app_context():
        # Create a test supplier
        test_supplier = Supplier(
            name="Test Supplier",
            phone="123456789",
            email="<EMAIL>",
            address="Test Address"
        )
        
        # Create a test medicine
        from datetime import date, datetime
        test_medicine = Medicine(
            name="Test Medicine",
            category="Test Category", 
            price=10.50,
            quantity=100,
            expiry_date=date(2025, 12, 31),
            supplier_id=1,
            description="Test Description"
        )
        test_medicine.supplier = test_supplier
        
        print("✅ Test objects created")
        
        # Test to_dict method
        try:
            medicine_dict = test_medicine.to_dict()
            print("✅ Medicine to_dict() works")
            print(f"Medicine dict: {medicine_dict}")
            
            # Test JSON serialization
            json_str = json.dumps(medicine_dict)
            print("✅ JSON serialization works")
            print(f"JSON: {json_str}")
            
            # Test with jsonify
            with app.test_request_context():
                response = jsonify([medicine_dict])
                print("✅ Flask jsonify works")
                print(f"Response data: {response.get_data(as_text=True)}")
                
        except Exception as e:
            print(f"❌ Error in JSON serialization: {e}")
            
        print("\n🎉 All tests passed! The JSON serialization fix is working correctly.")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
