@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    بناء نظام إدارة الصيدلية المحسن - EXE
echo ========================================
echo.

echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت PyInstaller
    pause
    exit /b 1
)

echo.
echo 🔧 تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo 🏗️  بناء الملف التنفيذي المحسن...
pyinstaller --onefile --windowed --name "نظام_إدارة_الصيدلية_محسن" --add-data "templates;templates" --add-data "static;static" --icon=pharmacy.ico run_exe_fixed.py

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء الملف التنفيذي
    echo 🔄 محاولة بناء بدون أيقونة...
    pyinstaller --onefile --windowed --name "نظام_إدارة_الصيدلية_محسن" --add-data "templates;templates" --add-data "static;static" run_exe_fixed.py
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء الملف التنفيذي نهائياً
        pause
        exit /b 1
    )
)

echo.
echo 📁 نسخ الملفات الإضافية...
if exist "pharmacy.db" copy "pharmacy.db" "dist\"
if exist "config.py" copy "config.py" "dist\"

echo.
echo ✅ تم بناء الملف التنفيذي المحسن بنجاح!
echo.
echo 📍 الملف التنفيذي موجود في:
echo    dist\نظام_إدارة_الصيدلية_محسن.exe
echo.
echo 🚀 لتشغيل التطبيق:
echo    1. انتقل إلى مجلد dist
echo    2. شغل ملف نظام_إدارة_الصيدلية_محسن.exe
echo.
echo 📝 ملاحظات مهمة:
echo    - سيتم فتح المتصفح تلقائياً
echo    - المستخدم: admin
echo    - كلمة المرور: admin123
echo    - الرابط: http://127.0.0.1:5000
echo.
pause