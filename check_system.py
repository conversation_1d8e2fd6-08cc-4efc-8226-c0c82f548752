#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص النظام والتأكد من سلامة جميع المكونات
"""

import os
import sys
from datetime import datetime, date, timedelta

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("🔍 فحص الملفات...")
    
    required_files = [
        'app.py',
        'config.py',
        'templates/base.html',
        'templates/index.html',
        'templates/medicines.html',
        'templates/add_medicine.html',
        'templates/edit_medicine.html',
        'templates/customers.html',
        'templates/add_customer.html',
        'templates/edit_customer.html',
        'templates/suppliers.html',
        'templates/add_supplier.html',
        'templates/edit_supplier.html',
        'templates/sales.html',
        'templates/new_sale.html',
        'templates/reports.html',
        'templates/sales_report.html',
        'templates/inventory_report.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ جميع الملفات موجودة")
        return True

def check_imports():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات...")
    
    required_modules = [
        'flask',
        'flask_sqlalchemy'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ مكتبات مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n💡 لتثبيت المكتبات المفقودة:")
        print("pip install flask flask-sqlalchemy")
        return False
    else:
        print("✅ جميع المكتبات متوفرة")
        return True

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️  فحص قاعدة البيانات...")
    
    try:
        from app import app, db, Medicine, Customer, Supplier, Sale, SaleItem
        
        with app.app_context():
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()
            
            # فحص الجداول
            tables = db.engine.table_names()
            required_tables = ['medicines', 'customers', 'suppliers', 'sales', 'sale_items']
            
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                print("❌ جداول مفقودة:")
                for table in missing_tables:
                    print(f"   - {table}")
                return False
            else:
                print("✅ جميع جداول قاعدة البيانات موجودة")
                
                # إحصائيات سريعة
                medicine_count = Medicine.query.count()
                customer_count = Customer.query.count()
                supplier_count = Supplier.query.count()
                sale_count = Sale.query.count()
                
                print(f"📊 الإحصائيات:")
                print(f"   - الأدوية: {medicine_count}")
                print(f"   - العملاء: {customer_count}")
                print(f"   - الموردين: {supplier_count}")
                print(f"   - المبيعات: {sale_count}")
                
                return True
                
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def check_templates():
    """فحص القوالب للتأكد من عدم وجود أخطاء"""
    print("\n🎨 فحص القوالب...")
    
    try:
        from app import app
        
        with app.app_context():
            # محاولة تحميل القوالب الأساسية
            from flask import render_template_string
            
            # فحص استخدام moment() في القوالب
            template_files = [
                'templates/new_sale.html',
                'templates/reports.html',
                'templates/edit_medicine.html'
            ]
            
            issues = []
            for template_file in template_files:
                if os.path.exists(template_file):
                    with open(template_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'moment().strftime' in content:
                            issues.append(f"{template_file}: استخدام moment().strftime")
                        if 'moment() -' in content:
                            issues.append(f"{template_file}: استخدام moment() في العمليات الحسابية")
            
            if issues:
                print("⚠️  مشاكل محتملة في القوالب:")
                for issue in issues:
                    print(f"   - {issue}")
                return False
            else:
                print("✅ القوالب تبدو سليمة")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في فحص القوالب: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏥 فحص نظام إدارة الصيدلية")
    print("=" * 60)
    
    checks = [
        check_files(),
        check_imports(),
        check_database(),
        check_templates()
    ]
    
    print("\n" + "=" * 60)
    if all(checks):
        print("✅ النظام جاهز للتشغيل!")
        print("\n🚀 لتشغيل النظام:")
        print("python run.py")
    else:
        print("❌ يوجد مشاكل تحتاج إلى إصلاح")
        print("\n💡 يرجى إصلاح المشاكل المذكورة أعلاه")
    
    print("=" * 60)

if __name__ == '__main__':
    main()