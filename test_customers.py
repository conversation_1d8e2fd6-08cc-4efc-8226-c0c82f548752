#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to check customers functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, Customer
    from flask import url_for
    
    print("✅ All imports successful")
    
    # Test the customers functionality
    with app.app_context():
        # Test customers route
        with app.test_client() as client:
            print("🧪 Testing customers page...")
            
            # Test GET request to customers page
            response = client.get('/customers')
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Customers page loads successfully")
                print(f"Response length: {len(response.data)} bytes")
            else:
                print(f"❌ Customers page failed with status: {response.status_code}")
                print(f"Response: {response.data.decode('utf-8')}")
            
            # Test add customer page
            print("\n🧪 Testing add customer page...")
            response = client.get('/customers/add')
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Add customer page loads successfully")
            else:
                print(f"❌ Add customer page failed with status: {response.status_code}")
                print(f"Response: {response.data.decode('utf-8')}")
            
            # Test adding a customer
            print("\n🧪 Testing customer creation...")
            response = client.post('/customers/add', data={
                'name': 'Test Customer',
                'phone': '0512345678',
                'email': '<EMAIL>',
                'address': 'Test Address'
            })
            
            print(f"Status code: {response.status_code}")
            if response.status_code == 302:  # Redirect after successful creation
                print("✅ Customer creation successful (redirected)")
            else:
                print(f"❌ Customer creation failed with status: {response.status_code}")
                print(f"Response: {response.data.decode('utf-8')}")
            
            # Check if customer was created
            customer = Customer.query.filter_by(name='Test Customer').first()
            if customer:
                print("✅ Customer was created in database")
                print(f"Customer: {customer.name}, Phone: {customer.phone}")
                
                # Test edit customer page
                print(f"\n🧪 Testing edit customer page for ID {customer.id}...")
                response = client.get(f'/customers/edit/{customer.id}')
                print(f"Status code: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ Edit customer page loads successfully")
                else:
                    print(f"❌ Edit customer page failed with status: {response.status_code}")
                    print(f"Response: {response.data.decode('utf-8')}")
                
                # Clean up - delete test customer
                db.session.delete(customer)
                db.session.commit()
                print("🧹 Test customer cleaned up")
            else:
                print("❌ Customer was not created in database")
                
        print("\n🎉 Customer functionality test completed!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
