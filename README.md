# نظام إدارة الصيدلية 💊

نظام شامل لإدارة الصيدليات باللغة العربية مع واجهة جذابة وألوان جميلة، مبني باستخدام Flask و Bootstrap.

## المميزات الرئيسية ✨

### 🏠 الصفحة الرئيسية
- لوحة تحكم شاملة مع إحصائيات سريعة
- تنبيهات للأدوية منتهية الصلاحية
- تنبيهات للأدوية قليلة المخزون
- ملخص مبيعات اليوم

### 💊 إدارة الأدوية
- إضافة وتعديل وحذف الأدوية
- تصنيف الأدوية حسب الفئات
- تتبع تواريخ انتهاء الصلاحية
- إدارة المخزون والكميات
- ربط الأدوية بالموردين

### 👥 إدارة العملاء
- إضافة وتعديل معلومات العملاء
- تتبع تاريخ المشتريات
- إحصائيات العملاء النشطين

### 🚚 إدارة الموردين
- إدارة معلومات الموردين
- ربط الأدوية بالموردين
- تتبع الأدوية لكل مورد

### 🛒 نظام المبيعات
- إنشاء فواتير جديدة
- البحث السريع عن الأدوية
- حساب الخصومات تلقائياً
- طباعة الفواتير
- تتبع المبيعات اليومية

### 📊 التقارير والإحصائيات
- تقارير المبيعات (يومية، أسبوعية، شهرية)
- تقارير المخزون
- تقارير العملاء والموردين
- إحصائيات شاملة

## متطلبات النظام 🔧

- Python 3.7+
- MySQL Server
- متصفح ويب حديث

## التثبيت والإعداد 🚀

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd pharmacy-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
venv\Scripts\activate  # على Windows
# أو
source venv/bin/activate  # على Linux/Mac
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات MySQL

#### تثبيت MySQL
- قم بتحميل وتثبيت MySQL Server من الموقع الرسمي
- أنشئ مستخدم جديد أو استخدم المستخدم الافتراضي `root`

#### تحديث إعدادات قاعدة البيانات
قم بتعديل ملف `.env` وضع إعدادات قاعدة البيانات الخاصة بك:
```
DATABASE_URL=mysql+pymysql://username:password@localhost/pharmacy_db
```

### 5. إنشاء قاعدة البيانات والبيانات التجريبية
```bash
python create_db.py
```

### 6. تشغيل التطبيق
```bash
python app.py
```

### 7. فتح التطبيق
افتح متصفحك وانتقل إلى: `http://localhost:5000`

## هيكل المشروع 📁

```
pharmacy-management-system/
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات التطبيق
├── create_db.py          # إنشاء قاعدة البيانات
├── requirements.txt      # المتطلبات
├── .env                 # متغيرات البيئة
├── README.md            # هذا الملف
└── templates/           # قوالب HTML
    ├── base.html        # القالب الأساسي
    ├── index.html       # الصفحة الرئيسية
    ├── medicines.html   # إدارة الأدوية
    ├── customers.html   # إدارة العملاء
    ├── suppliers.html   # إدارة الموردين
    ├── sales.html       # المبيعات
    ├── new_sale.html    # فاتورة جديدة
    ├── reports.html     # التقارير
    └── ...             # قوالب أخرى
```

## الاستخدام 📖

### إضافة دواء جديد
1. انتقل إلى "الأدوية" من القائمة الرئيسية
2. اضغط على "إضافة دواء جديد"
3. املأ المعلومات المطلوبة
4. اضغط "حفظ الدواء"

### إنشاء فاتورة جديدة
1. انتقل إلى "المبيعات" من القائمة الرئيسية
2. اضغط على "فاتورة جديدة"
3. ابحث عن الأدوية وأضفها للفاتورة
4. أضف خصم إذا لزم الأمر
5. اضغط "حفظ الفاتورة"

### عرض التقارير
1. انتقل إلى "التقارير" من القائمة الرئيسية
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية إذا لزم الأمر
4. اضغط "عرض التقرير"

## المميزات التقنية 🛠️

- **واجهة مستخدم عربية**: تصميم RTL كامل
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **ألوان جذابة**: تدرجات لونية حديثة
- **أمان البيانات**: حماية من SQL Injection
- **بحث سريع**: بحث فوري في الأدوية والعملاء
- **تنبيهات ذكية**: تنبيهات للمخزون والصلاحية
- **طباعة الفواتير**: إمكانية طباعة الفواتير

## التخصيص 🎨

يمكنك تخصيص التطبيق عبر:
- تعديل الألوان في ملف `templates/base.html`
- إضافة فئات أدوية جديدة في `templates/add_medicine.html`
- تخصيص التقارير في ملفات التقارير

## استكشاف الأخطاء 🔍

### مشاكل شائعة وحلولها:

#### خطأ في الاتصال بقاعدة البيانات
```
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server")
```
**الحل**: تأكد من تشغيل MySQL Server وصحة بيانات الاتصال في `.env`

#### خطأ في إنشاء الجداول
```
pymysql.err.ProgrammingError: (1146, "Table doesn't exist")
```
**الحل**: قم بتشغيل `python create_db.py` مرة أخرى

#### مشكلة في عرض النصوص العربية
**الحل**: تأكد من أن قاعدة البيانات تستخدم `utf8mb4` encoding

## الدعم والمساهمة 🤝

- لطلب المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue جديد
- المساهمات مرحب بها! يرجى إنشاء Pull Request

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الشكر والتقدير 🙏

- Bootstrap لتصميم الواجهة
- Font Awesome للأيقونات
- Flask لإطار العمل
- MySQL لقاعدة البيانات

---

**ملاحظة**: هذا النظام مصمم للاستخدام التعليمي والتجريبي. للاستخدام التجاري، يرجى مراجعة المتطلبات القانونية والتنظيمية في بلدك.