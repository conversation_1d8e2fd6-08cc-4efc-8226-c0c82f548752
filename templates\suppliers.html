{% extends "base.html" %}

{% block title %}إدارة الموردين - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-truck me-2"></i>إدارة الموردين
                </h5>
                <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                </a>
            </div>
            <div class="card-body">
                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" name="search" class="form-control me-2" 
                                   placeholder="البحث عن مورد..." value="{{ search }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- جدول الموردين -->
                {% if suppliers.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المورد</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                                <th>عدد الأدوية</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers.items %}
                            <tr>
                                <td>{{ supplier.id }}</td>
                                <td>
                                    <strong>{{ supplier.name }}</strong>
                                </td>
                                <td>
                                    {% if supplier.phone %}
                                        <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone me-1"></i>{{ supplier.phone }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if supplier.email %}
                                        <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                                            <i class="fas fa-envelope me-1"></i>{{ supplier.email }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if supplier.address %}
                                        {{ supplier.address[:30] }}{% if supplier.address|length > 30 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ supplier.medicines|length }}</span>
                                </td>
                                <td>{{ supplier.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#supplierModal{{ supplier.id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('edit_supplier', id=supplier.id) }}" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_supplier', id=supplier.id) }}" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟\nسيتم حذف جميع الأدوية المرتبطة به!')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal لعرض تفاصيل المورد -->
                            <div class="modal fade" id="supplierModal{{ supplier.id }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تفاصيل المورد: {{ supplier.name }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>المعلومات الأساسية</h6>
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>الاسم:</strong></td>
                                                            <td>{{ supplier.name }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>الهاتف:</strong></td>
                                                            <td>{{ supplier.phone or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>البريد:</strong></td>
                                                            <td>{{ supplier.email or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>العنوان:</strong></td>
                                                            <td>{{ supplier.address or 'غير محدد' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>تاريخ التسجيل:</strong></td>
                                                            <td>{{ supplier.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>إحصائيات الأدوية</h6>
                                                    {% set total_medicines = supplier.medicines|length %}
                                                    {% set total_value = 0 %}
                                                    {% for medicine in supplier.medicines %}
                                                        {% set total_value = total_value + (medicine.price * medicine.quantity) %}
                                                    {% endfor %}
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>عدد الأدوية:</strong></td>
                                                            <td><span class="badge bg-primary">{{ total_medicines }}</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>إجمالي قيمة المخزون:</strong></td>
                                                            <td><span class="badge bg-success">{{ "%.0f"|format(total_value) }} دينار</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>أدوية قليلة المخزون:</strong></td>
                                                            <td>{{ supplier.medicines|selectattr('quantity', 'le', 10)|list|length }}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            
                                            {% if supplier.medicines %}
                                            <hr>
                                            <h6>الأدوية المتوفرة</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>اسم الدواء</th>
                                                            <th>الفئة</th>
                                                            <th>السعر</th>
                                                            <th>الكمية</th>
                                                            <th>تاريخ الانتهاء</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for medicine in supplier.medicines[:10] %}
                                                        <tr>
                                                            <td>{{ medicine.name }}</td>
                                                            <td><span class="badge bg-info">{{ medicine.category }}</span></td>
                                                            <td>{{ "%.0f"|format(medicine.price) }} دينار</td>
                                                            <td>
                                                                {% if medicine.quantity <= 10 %}
                                                                    <span class="badge bg-warning">{{ medicine.quantity }}</span>
                                                                {% else %}
                                                                    <span class="badge bg-success">{{ medicine.quantity }}</span>
                                                                {% endif %}
                                                            </td>
                                                            <td>{{ medicine.expiry_date.strftime('%Y-%m-%d') }}</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                                {% if supplier.medicines|length > 10 %}
                                                <p class="text-muted text-center">
                                                    وهناك {{ supplier.medicines|length - 10 }} أدوية أخرى...
                                                </p>
                                                {% endif %}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            <a href="{{ url_for('edit_supplier', id=supplier.id) }}" class="btn btn-primary">تعديل</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if suppliers.pages > 1 %}
                <nav aria-label="صفحات الموردين">
                    <ul class="pagination justify-content-center">
                        {% if suppliers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('suppliers', page=suppliers.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in suppliers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != suppliers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('suppliers', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if suppliers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('suppliers', page=suppliers.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد موردين</h5>
                    <p class="text-muted">ابدأ بإضافة أول مورد في النظام</p>
                    <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الموردين -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-truck fa-2x mb-2"></i>
                <h6>إجمالي الموردين</h6>
                <h4>{{ suppliers.total }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h6>موردين نشطين</h6>
                <h4>{{ suppliers.items|selectattr('medicines')|list|length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-pills fa-2x mb-2"></i>
                <h6>إجمالي الأدوية</h6>
                <h4>
                    {% set total_medicines = 0 %}
                    {% for supplier in suppliers.items %}
                        {% set total_medicines = total_medicines + supplier.medicines|length %}
                    {% endfor %}
                    {{ total_medicines }}
                </h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}