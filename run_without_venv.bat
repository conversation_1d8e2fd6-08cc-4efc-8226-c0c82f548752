@echo off
chcp 65001 >nul
echo.
echo ========================================
echo  تشغيل نظام الصيدلية بدون بيئة افتراضية
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 تأكد من تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت Flask في النظام العام...
pip install Flask Flask-SQLAlchemy
if %errorlevel% neq 0 (
    echo ⚠️ فشل في التثبيت العادي، جرب مع --user...
    pip install --user Flask Flask-SQLAlchemy
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Flask
        echo 💡 جرب تشغيل Command Prompt كمدير
        pause
        exit /b 1
    )
)

echo.
echo 🧪 اختبار Flask...
python -c "import flask; print('✅ Flask يعمل بشكل صحيح')"
if %errorlevel% neq 0 (
    echo ❌ Flask لا يعمل، جرب النسخة المبسطة
    echo.
    echo 🔄 تشغيل النسخة المبسطة...
    python run_exe_fixed.py
    pause
    exit /b 0
)

echo.
echo 🚀 تشغيل نظام إدارة الصيدلية...
echo 📱 سيفتح المتصفح على: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo ========================================
echo.

python app.py

pause
