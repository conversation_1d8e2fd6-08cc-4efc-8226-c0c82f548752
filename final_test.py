#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Final comprehensive test for all functionality
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, Medicine, Customer, Supplier
    
    print("🎯 Final Comprehensive Test")
    print("=" * 40)
    
    # Test all main pages
    with app.app_context():
        with app.test_client() as client:
            
            pages_to_test = [
                ('/', 'الصفحة الرئيسية'),
                ('/medicines', 'صفحة الأدوية'),
                ('/medicines/add', 'إضافة دواء'),
                ('/customers', 'صفحة العملاء'),
                ('/customers/add', 'إضافة عميل'),
                ('/suppliers', 'صفحة الموردين'),
                ('/suppliers/add', 'إضافة مورد'),
                ('/sales', 'صفحة المبيعات'),
                ('/sales/new', 'فاتورة جديدة'),
                ('/reports', 'صفحة التقارير'),
                ('/api/medicines/search?q=test', 'API البحث عن الأدوية')
            ]
            
            all_passed = True
            
            for url, name in pages_to_test:
                try:
                    response = client.get(url)
                    if response.status_code == 200:
                        print(f"✅ {name}: يعمل بشكل صحيح")
                    else:
                        print(f"❌ {name}: خطأ {response.status_code}")
                        all_passed = False
                except Exception as e:
                    print(f"❌ {name}: خطأ - {e}")
                    all_passed = False
            
            print("\n" + "=" * 40)
            
            if all_passed:
                print("🎉 جميع الصفحات تعمل بشكل صحيح!")
                print("\n📋 يمكنك الآن تشغيل التطبيق باستخدام:")
                print("   python app.py")
                print("   أو")
                print("   python start_server.py")
                print("\n🌐 ثم افتح المتصفح على:")
                print("   http://127.0.0.1:5000")
                
                print("\n✨ الميزات المتاحة:")
                print("   ✅ إدارة الأدوية (إضافة، تعديل، حذف)")
                print("   ✅ إدارة العملاء (إضافة، تعديل، حذف)")
                print("   ✅ إدارة الموردين (إضافة، تعديل، حذف)")
                print("   ✅ نظام المبيعات والفواتير")
                print("   ✅ التقارير والإحصائيات")
                print("   ✅ البحث والتصفية")
                print("   ✅ تنبيهات انتهاء الصلاحية")
                print("   ✅ تنبيهات نقص المخزون")
                
            else:
                print("❌ هناك مشاكل في بعض الصفحات")
                
        print("\n" + "=" * 40)
        print("🏁 انتهى الاختبار")
        
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
