{% extends "base.html" %}

{% block title %}إدارة الأدوية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-pills me-2"></i>إدارة الأدوية
                </h5>
                <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة دواء جديد
                </a>
            </div>
            <div class="card-body">
                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" name="search" class="form-control me-2" 
                                   placeholder="البحث عن دواء..." value="{{ search }}">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- جدول الأدوية -->
                {% if medicines.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الدواء</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>المورد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in medicines.items %}
                            <tr class="{% if medicine.quantity <= 10 %}table-warning{% endif %} 
                                       {% if medicine.expiry_date <= (moment().date() + timedelta(days=30)) %}table-danger{% endif %}">
                                <td>{{ medicine.id }}</td>
                                <td>
                                    <strong>{{ medicine.name }}</strong>
                                    {% if medicine.description %}
                                        <br><small class="text-muted">{{ medicine.description[:50] }}...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ medicine.category }}</span>
                                </td>
                                <td>{{ "%.0f"|format(medicine.price) }} دينار</td>
                                <td>
                                    {% if medicine.quantity <= 10 %}
                                        <span class="badge bg-warning">{{ medicine.quantity }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ medicine.quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% set days_to_expiry = (medicine.expiry_date - moment().date()).days %}
                                    {% if days_to_expiry <= 0 %}
                                        <span class="badge bg-danger">منتهي الصلاحية</span>
                                    {% elif days_to_expiry <= 30 %}
                                        <span class="badge bg-warning">{{ days_to_expiry }} يوم</span>
                                    {% else %}
                                        {{ medicine.expiry_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </td>
                                <td>{{ medicine.supplier.name if medicine.supplier else 'غير محدد' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_medicine', id=medicine.id) }}" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('delete_medicine', id=medicine.id) }}" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if medicines.pages > 1 %}
                <nav aria-label="صفحات الأدوية">
                    <ul class="pagination justify-content-center">
                        {% if medicines.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('medicines', page=medicines.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in medicines.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != medicines.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('medicines', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if medicines.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('medicines', page=medicines.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية</h5>
                    <p class="text-muted">ابدأ بإضافة أول دواء في النظام</p>
                    <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة دواء جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h6>أدوية قليلة المخزون</h6>
                <p class="mb-0">الأدوية التي كميتها 10 أو أقل</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-times fa-2x mb-2"></i>
                <h6>أدوية منتهية الصلاحية</h6>
                <p class="mb-0">الأدوية المنتهية أو قريبة من الانتهاء</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h6>أدوية متوفرة</h6>
                <p class="mb-0">الأدوية المتوفرة بكميات كافية</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إضافة moment.js للتواريخ
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة JavaScript إضافي هنا
});
</script>
{% endblock %}