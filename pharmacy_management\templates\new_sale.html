{% extends "base.html" %}

{% block title %}New Sale - Pharmacy Management System{% endblock %}

{% block page_title %}New Sale{% endblock %}

{% block page_actions %}
<a href="{{ url_for('sales') }}" class="btn btn-secondary">
    <i class="fas fa-arrow-left"></i> Back to Sales
</a>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Customer Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="customer_name" class="form-label">Customer Name</label>
                    <input type="text" class="form-control" id="customer_name" placeholder="Optional">
                </div>
                <div class="mb-3">
                    <label for="customer_phone" class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" id="customer_phone" placeholder="Optional">
                </div>
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select class="form-select" id="payment_method">
                        <option value="cash">Cash</option>
                        <option value="card">Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Medicine Selection -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Add Medicines</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="medicine_select" class="form-label">Select Medicine</label>
                        <select class="form-select" id="medicine_select">
                            <option value="">Choose a medicine...</option>
                            {% for medicine in medicines %}
                            <option value="{{ medicine.id }}" 
                                    data-name="{{ medicine.name }}" 
                                    data-price="{{ medicine.unit_price }}" 
                                    data-stock="{{ medicine.stock_quantity }}">
                                {{ medicine.name }} - {{ medicine.strength }} (Stock: {{ medicine.stock_quantity }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" min="1" value="1">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-primary w-100" onclick="addMedicine()">
                            <i class="fas fa-plus"></i> Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sale Items -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Sale Items</h6>
                <div class="text-end">
                    <strong>Total: <span id="total_amount" class="currency">0</span></strong>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="sale_items_table">
                        <thead class="table-light">
                            <tr>
                                <th>Medicine</th>
                                <th>Unit Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="sale_items">
                            <tr id="no_items_row">
                                <td colspan="5" class="text-center text-muted">No items added yet</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-secondary" onclick="clearSale()">
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-success btn-lg" onclick="processSale()" id="process_sale_btn" disabled>
                            <i class="fas fa-check"></i> Process Sale
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let saleItems = [];
let totalAmount = 0;

function addMedicine() {
    const medicineSelect = document.getElementById('medicine_select');
    const quantityInput = document.getElementById('quantity');
    
    const selectedOption = medicineSelect.options[medicineSelect.selectedIndex];
    const medicineId = selectedOption.value;
    const medicineName = selectedOption.dataset.name;
    const unitPrice = parseFloat(selectedOption.dataset.price);
    const availableStock = parseInt(selectedOption.dataset.stock);
    const quantity = parseInt(quantityInput.value);
    
    if (!medicineId) {
        alert('Please select a medicine');
        return;
    }
    
    if (quantity <= 0) {
        alert('Please enter a valid quantity');
        return;
    }
    
    if (quantity > availableStock) {
        alert(`Insufficient stock. Available: ${availableStock}`);
        return;
    }
    
    // Check if medicine already exists in sale
    const existingItemIndex = saleItems.findIndex(item => item.medicine_id == medicineId);
    
    if (existingItemIndex >= 0) {
        // Update existing item
        const newQuantity = saleItems[existingItemIndex].quantity + quantity;
        if (newQuantity > availableStock) {
            alert(`Total quantity would exceed available stock. Available: ${availableStock}`);
            return;
        }
        saleItems[existingItemIndex].quantity = newQuantity;
        saleItems[existingItemIndex].total_price = newQuantity * unitPrice;
    } else {
        // Add new item
        saleItems.push({
            medicine_id: medicineId,
            medicine_name: medicineName,
            unit_price: unitPrice,
            quantity: quantity,
            total_price: quantity * unitPrice
        });
    }
    
    updateSaleItemsTable();
    
    // Reset form
    medicineSelect.value = '';
    quantityInput.value = 1;
}

function updateSaleItemsTable() {
    const tbody = document.getElementById('sale_items');
    const noItemsRow = document.getElementById('no_items_row');
    
    if (saleItems.length === 0) {
        noItemsRow.style.display = '';
        document.getElementById('process_sale_btn').disabled = true;
        totalAmount = 0;
    } else {
        noItemsRow.style.display = 'none';
        document.getElementById('process_sale_btn').disabled = false;
        
        // Clear existing rows (except no_items_row)
        const rows = tbody.querySelectorAll('tr:not(#no_items_row)');
        rows.forEach(row => row.remove());
        
        // Add sale items
        totalAmount = 0;
        saleItems.forEach((item, index) => {
            totalAmount += item.total_price;
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.medicine_name}</td>
                <td><span class="currency">${item.unit_price}</span></td>
                <td>
                    <input type="number" class="form-control form-control-sm" 
                           value="${item.quantity}" min="1" 
                           onchange="updateQuantity(${index}, this.value)"
                           style="width: 80px;">
                </td>
                <td><span class="currency">${item.total_price}</span></td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger" 
                            onclick="removeItem(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    document.getElementById('total_amount').textContent = totalAmount.toFixed(2);
    
    // Format currency
    const currencyElements = document.querySelectorAll('.currency');
    currencyElements.forEach(function(element) {
        const amount = parseFloat(element.textContent);
        if (!isNaN(amount)) {
            element.textContent = formatCurrency(amount);
        }
    });
}

function updateQuantity(index, newQuantity) {
    const quantity = parseInt(newQuantity);
    if (quantity <= 0) {
        removeItem(index);
        return;
    }
    
    saleItems[index].quantity = quantity;
    saleItems[index].total_price = quantity * saleItems[index].unit_price;
    updateSaleItemsTable();
}

function removeItem(index) {
    saleItems.splice(index, 1);
    updateSaleItemsTable();
}

function clearSale() {
    if (saleItems.length > 0 && !confirm('Are you sure you want to clear all items?')) {
        return;
    }
    saleItems = [];
    updateSaleItemsTable();
}

function processSale() {
    if (saleItems.length === 0) {
        alert('Please add at least one item to the sale');
        return;
    }
    
    const customerName = document.getElementById('customer_name').value;
    const customerPhone = document.getElementById('customer_phone').value;
    const paymentMethod = document.getElementById('payment_method').value;
    
    const saleData = {
        customer_name: customerName,
        customer_phone: customerPhone,
        payment_method: paymentMethod,
        total_amount: totalAmount,
        items: saleItems
    };
    
    // Disable button to prevent double submission
    const processBtn = document.getElementById('process_sale_btn');
    processBtn.disabled = true;
    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    fetch('/process_sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Sale processed successfully! Sale ID: #${data.sale_id}`);
            window.location.href = '/sales';
        } else {
            alert('Error processing sale: ' + data.message);
            processBtn.disabled = false;
            processBtn.innerHTML = '<i class="fas fa-check"></i> Process Sale';
        }
    })
    .catch(error => {
        alert('Error processing sale: ' + error.message);
        processBtn.disabled = false;
        processBtn.innerHTML = '<i class="fas fa-check"></i> Process Sale';
    });
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0
    }).format(amount);
}
</script>
{% endblock %}
