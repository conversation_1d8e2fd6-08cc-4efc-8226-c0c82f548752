#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام إدارة الصيدلية
"""

from app import app, db

def create_tables():
    """إنشاء جداول قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")

def run_app():
    """تشغيل التطبيق"""
    print("🚀 بدء تشغيل نظام إدارة الصيدلية...")
    print("📊 الوصول للتطبيق:")
    print("   - محلي: http://127.0.0.1:5000")
    print("   - شبكة: http://*************:5000")
    print("⚠️  اضغط Ctrl+C لإيقاف التطبيق")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == '__main__':
    create_tables()
    run_app()