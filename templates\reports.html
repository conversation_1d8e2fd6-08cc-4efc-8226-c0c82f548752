{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="text-center mb-4">
            <i class="fas fa-chart-bar text-primary me-3"></i>
            التقارير والإحصائيات
        </h2>
    </div>
</div>

<!-- أنواع التقارير -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>تقارير المبيعات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">عرض تقارير مفصلة عن المبيعات والإيرادات</p>
                
                <form action="{{ url_for('sales_report') }}" method="GET" class="mb-3">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>
                        <div class="col-md-6 mb-2">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>عرض تقرير المبيعات
                    </button>
                </form>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('sales_report') }}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-day me-2"></i>مبيعات اليوم
                    </a>
                    <a href="{{ url_for('sales_report') }}?days=7" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-calendar-week me-2"></i>مبيعات الأسبوع
                    </a>
                    <a href="{{ url_for('sales_report') }}?days=30" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-calendar-alt me-2"></i>مبيعات الشهر
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>تقارير المخزون
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">عرض تقارير مفصلة عن المخزون والأدوية</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('inventory_report_page') }}" class="btn btn-success w-100">
                        <i class="fas fa-warehouse me-2"></i>تقرير المخزون الكامل
                    </a>
                    <a href="{{ url_for('inventory_report_page', filter='low_stock') }}" class="btn btn-outline-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>أدوية قليلة المخزون
                    </a>
                    <a href="{{ url_for('inventory_report_page', filter='expired') }}" class="btn btn-outline-danger">
                        <i class="fas fa-calendar-times me-2"></i>أدوية منتهية الصلاحية
                    </a>
                    <a href="{{ url_for('inventory_report_page', filter='expiring') }}" class="btn btn-outline-warning">
                        <i class="fas fa-clock me-2"></i>أدوية قريبة من الانتهاء
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>تقارير العملاء
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إحصائيات ومعلومات عن العملاء</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('customers_report') }}" class="btn btn-info w-100">
                        <i class="fas fa-list me-2"></i>قائمة العملاء الكاملة
                    </a>
                    <a href="{{ url_for('customers_report', filter='active') }}" class="btn btn-outline-info">
                        <i class="fas fa-user-check me-2"></i>العملاء النشطين
                    </a>
                    <a href="{{ url_for('customers_report', filter='top') }}" class="btn btn-outline-info">
                        <i class="fas fa-crown me-2"></i>أفضل العملاء
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-truck me-2"></i>تقارير الموردين
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">معلومات وإحصائيات عن الموردين</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('suppliers_report') }}" class="btn btn-warning w-100">
                        <i class="fas fa-list me-2"></i>قائمة الموردين الكاملة
                    </a>
                    <a href="{{ url_for('suppliers_report', filter='active') }}" class="btn btn-outline-warning">
                        <i class="fas fa-check-circle me-2"></i>الموردين النشطين
                    </a>
                    <a href="{{ url_for('suppliers_report', filter='medicines_count') }}" class="btn btn-outline-warning">
                        <i class="fas fa-pills me-2"></i>حسب عدد الأدوية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-pills fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">{{ total_medicines or 0 }}</h4>
                            <small class="text-muted">إجمالي الأدوية</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-users fa-2x text-success mb-2"></i>
                            <h4 class="text-success">{{ total_customers or 0 }}</h4>
                            <small class="text-muted">إجمالي العملاء</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-truck fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">{{ total_suppliers or 0 }}</h4>
                            <small class="text-muted">إجمالي الموردين</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-shopping-cart fa-2x text-info mb-2"></i>
                            <h4 class="text-info">{{ total_sales or 0 }}</h4>
                            <small class="text-muted">إجمالي المبيعات</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-money-bill fa-2x text-success mb-2"></i>
                            <h4 class="text-success">{{ "%.0f"|format(total_revenue or 0) }}</h4>
                            <small class="text-muted">إجمالي الإيرادات</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border rounded p-3 mb-3">
                            <i class="fas fa-calendar-day fa-2x text-primary mb-2"></i>
                            <h4 class="text-primary">{{ "%.0f"|format(today_revenue or 0) }}</h4>
                            <small class="text-muted">إيرادات اليوم</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات مهمة -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>تنبيهات المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <h4 class="text-warning">{{ low_stock_count or 0 }}</h4>
                        <small>أدوية قليلة المخزون</small>
                    </div>
                    <div class="col-6 text-center">
                        <h4 class="text-danger">{{ expired_count or 0 }}</h4>
                        <small>أدوية منتهية الصلاحية</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>أداء المبيعات
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <h4 class="text-info">{{ monthly_sales or 0 }}</h4>
                        <small>مبيعات الشهر</small>
                    </div>
                    <div class="col-6 text-center">
                        <h4 class="text-success">{{ "%.0f"|format(monthly_revenue or 0) }}</h4>
                        <small>إيرادات الشهر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تعيين التاريخ الحالي كقيمة افتراضية
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('end_date').value = today;
    
    // تعيين بداية الشهر كتاريخ البداية
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    document.getElementById('start_date').value = firstDay;
});
</script>
{% endblock %}