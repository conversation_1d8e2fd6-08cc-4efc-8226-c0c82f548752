{% extends "base.html" %}

{% block title %}إدارة المبيعات - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>إدارة المبيعات
                </h5>
                <a href="{{ url_for('new_sale') }}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>فاتورة جديدة
                </a>
            </div>
            <div class="card-body">
                <!-- فلاتر البحث -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="start_date" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="end_date" placeholder="إلى تاريخ">
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="customer_search" placeholder="البحث بالعميل...">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="filterSales()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <!-- جدول المبيعات -->
                {% if sales.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>إجمالي المبلغ</th>
                                <th>الخصم</th>
                                <th>المبلغ النهائي</th>
                                <th>عدد الأصناف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales.items %}
                            <tr>
                                <td>
                                    <strong>#{{ sale.id }}</strong>
                                </td>
                                <td>
                                    {% if sale.customer %}
                                        <i class="fas fa-user me-1"></i>{{ sale.customer.name }}
                                    {% else %}
                                        <span class="text-muted">عميل غير مسجل</span>
                                    {% endif %}
                                </td>
                                <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>{{ "%.0f"|format(sale.total_amount) }} دينار</td>
                                <td>
                                    {% if sale.discount > 0 %}
                                        <span class="badge bg-warning">{{ "%.0f"|format(sale.discount) }} دينار</span>
                                    {% else %}
                                        <span class="text-muted">لا يوجد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong class="text-success">{{ "%.0f"|format(sale.final_amount) }} دينار</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ sale.sale_items|length }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#saleModal{{ sale.id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-success" 
                                                onclick="printInvoice({{ sale.id }})">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <a href="{{ url_for('delete_sale', id=sale.id) }}" 
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal لعرض تفاصيل الفاتورة -->
                            <div class="modal fade" id="saleModal{{ sale.id }}" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">تفاصيل الفاتورة #{{ sale.id }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <h6>معلومات الفاتورة</h6>
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>رقم الفاتورة:</strong></td>
                                                            <td>#{{ sale.id }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>التاريخ:</strong></td>
                                                            <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>العميل:</strong></td>
                                                            <td>{{ sale.customer.name if sale.customer else 'عميل غير مسجل' }}</td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>ملخص المبالغ</h6>
                                                    <table class="table table-sm">
                                                        <tr>
                                                            <td><strong>إجمالي المبلغ:</strong></td>
                                                            <td>{{ "%.0f"|format(sale.total_amount) }} دينار</td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>الخصم:</strong></td>
                                                            <td>{{ "%.0f"|format(sale.discount) }} دينار</td>
                                                        </tr>
                                                        <tr class="table-success">
                                                            <td><strong>المبلغ النهائي:</strong></td>
                                                            <td><strong>{{ "%.0f"|format(sale.final_amount) }} دينار</strong></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                            
                                            <h6>تفاصيل الأصناف</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>الدواء</th>
                                                            <th>الكمية</th>
                                                            <th>سعر الوحدة</th>
                                                            <th>الإجمالي</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for item in sale.sale_items %}
                                                        <tr>
                                                            <td>{{ item.medicine.name }}</td>
                                                            <td>{{ item.quantity }}</td>
                                                            <td>{{ "%.0f"|format(item.unit_price) }} دينار</td>
                                                            <td>{{ "%.0f"|format(item.total_price) }} دينار</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                            <button type="button" class="btn btn-success" onclick="printInvoice({{ sale.id }})">
                                                <i class="fas fa-print me-2"></i>طباعة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if sales.pages > 1 %}
                <nav aria-label="صفحات المبيعات">
                    <ul class="pagination justify-content-center">
                        {% if sales.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('sales', page=sales.prev_num) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in sales.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != sales.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('sales', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if sales.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('sales', page=sales.next_num) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مبيعات</h5>
                    <p class="text-muted">ابدأ بإنشاء أول فاتورة</p>
                    <a href="{{ url_for('new_sale') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>فاتورة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات المبيعات -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h6>إجمالي الفواتير</h6>
                <h4>{{ sales.total }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill fa-2x mb-2"></i>
                <h6>إجمالي المبيعات</h6>
                <h4>{{ "%.0f"|format(sales.items|sum(attribute='final_amount')) }} دينار</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h6>متوسط الفاتورة</h6>
                <h4>{{ "%.0f"|format(sales.items|sum(attribute='final_amount') / sales.items|length if sales.items else 0) }} دينار</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="fas fa-percentage fa-2x mb-2"></i>
                <h6>إجمالي الخصومات</h6>
                <h4>{{ "%.0f"|format(sales.items|sum(attribute='discount')) }} دينار</h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterSales() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const customerSearch = document.getElementById('customer_search').value;
    
    let url = new URL(window.location.href);
    
    if (startDate) url.searchParams.set('start_date', startDate);
    if (endDate) url.searchParams.set('end_date', endDate);
    if (customerSearch) url.searchParams.set('customer', customerSearch);
    
    window.location.href = url.toString();
}

function printInvoice(saleId) {
    // فتح نافذة جديدة لطباعة الفاتورة
    window.open(`/sales/print/${saleId}`, '_blank', 'width=800,height=600');
}

// تعيين التاريخ الحالي كقيمة افتراضية
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('end_date').value = today;
    
    // تعيين بداية الشهر كتاريخ البداية
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    document.getElementById('start_date').value = firstDay;
});
</script>
{% endblock %}