# PowerShell script to build the pharmacy management system EXE

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    بناء نظام إدارة الصيدلية - EXE" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# تثبيت المتطلبات
Write-Host "📦 تثبيت المتطلبات..." -ForegroundColor Green
try {
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في تثبيت المتطلبات"
    }
} catch {
    Write-Host "❌ $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "🔧 تنظيف الملفات القديمة..." -ForegroundColor Green
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }

Write-Host ""
Write-Host "🏗️  بناء الملف التنفيذي..." -ForegroundColor Green
try {
    pyinstaller --clean pharmacy_app.spec
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في بناء الملف التنفيذي"
    }
} catch {
    Write-Host "❌ $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "📁 نسخ الملفات الإضافية..." -ForegroundColor Green
if (Test-Path "pharmacy.db") { Copy-Item "pharmacy.db" "dist\" }
if (Test-Path "config.py") { Copy-Item "config.py" "dist\" }

Write-Host ""
Write-Host "✅ تم بناء الملف التنفيذي بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 الملف التنفيذي موجود في:" -ForegroundColor Cyan
Write-Host "   dist\نظام_إدارة_الصيدلية.exe" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 لتشغيل التطبيق:" -ForegroundColor Cyan
Write-Host "   1. انتقل إلى مجلد dist" -ForegroundColor White
Write-Host "   2. شغل ملف نظام_إدارة_الصيدلية.exe" -ForegroundColor White
Write-Host ""
Read-Host "اضغط Enter للخروج"
